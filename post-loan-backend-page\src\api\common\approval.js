import request from '@/utils/request'

/**
 * 统一审批API工具类
 * <AUTHOR>
 * @date 2025-08-01
 */

/**
 * 创建审批API工厂函数
 * @param {string} baseUrl - 基础URL路径
 * @returns {object} 审批API对象
 */
export function createApprovalApi(baseUrl) {
  return {
    // 单个审批
    approve(data) {
      return request({
        url: `${baseUrl}/approve`,
        method: 'put',
        data: data
      })
    },

    // 批量审批
    batchApprove(data) {
      return request({
        url: `${baseUrl}/batchApprove`,
        method: 'put',
        data: data
      })
    },

    // 获取待审批列表
    getPendingList() {
      return request({
        url: `${baseUrl}/pending`,
        method: 'get'
      })
    },

    // 获取审批状态统计
    getStatistics() {
      return request({
        url: `${baseUrl}/statistics`,
        method: 'get'
      })
    }
  }
}

/**
 * 法诉费用审批API
 */
export const litigationCostApprovalApi = createApprovalApi('/litigation_cost_approval/litigation_cost_approval')

/**
 * 车辆订单审批API
 */
export const carOrderExamineApi = createApprovalApi('/vw_car_order_examine/vw_car_order_examine')

/**
 * 通用审批请求数据构造器
 */
export class ApprovalRequestBuilder {
  constructor() {
    this.data = {}
  }

  // 设置ID
  setId(id) {
    this.data.id = id
    return this
  }

  // 设置审批动作
  setApprovalAction(action) {
    this.data.approvalAction = action
    return this
  }

  // 设置拒绝原因
  setRejectReason(reason) {
    this.data.rejectReason = reason
    return this
  }

  // 设置备注
  setRemark(remark) {
    this.data.remark = remark
    return this
  }

  // 构建请求数据
  build() {
    return { ...this.data }
  }
}

/**
 * 批量审批请求数据构造器
 */
export class BatchApprovalRequestBuilder {
  constructor() {
    this.data = {}
  }

  // 设置ID列表
  setIds(ids) {
    this.data.ids = ids
    return this
  }

  // 设置审批动作
  setApprovalAction(action) {
    this.data.approvalAction = action
    return this
  }

  // 设置拒绝原因
  setRejectReason(reason) {
    this.data.rejectReason = reason
    return this
  }

  // 设置备注
  setRemark(remark) {
    this.data.remark = remark
    return this
  }

  // 构建请求数据
  build() {
    return { ...this.data }
  }
}

/**
 * 审批工具函数
 */
export const approvalUtils = {
  // 构建单个审批请求
  buildApprovalRequest(id, approvalAction, rejectReason, remark) {
    return new ApprovalRequestBuilder()
      .setId(id)
      .setApprovalAction(approvalAction)
      .setRejectReason(rejectReason)
      .setRemark(remark)
      .build()
  },

  // 构建批量审批请求
  buildBatchApprovalRequest(ids, approvalAction, rejectReason, remark) {
    return new BatchApprovalRequestBuilder()
      .setIds(ids)
      .setApprovalAction(approvalAction)
      .setRejectReason(rejectReason)
      .setRemark(remark)
      .build()
  },

  // 处理审批响应
  handleApprovalResponse(response, successMessage = '审批成功') {
    if (response.code === 200) {
      this.$message.success(successMessage)
      return true
    } else {
      this.$message.error(response.msg || '审批失败')
      return false
    }
  },

  // 处理批量审批响应
  handleBatchApprovalResponse(response, successMessage = '批量审批成功') {
    if (response.code === 200) {
      this.$message.success(successMessage)
      return true
    } else {
      this.$message.error(response.msg || '批量审批失败')
      return false
    }
  }
}

/**
 * 审批混入对象（用于Vue组件）
 */
export const approvalMixin = {
  data() {
    return {
      // 审批对话框显示状态
      approvalDialogVisible: false,
      // 批量审批对话框显示状态
      batchApprovalDialogVisible: false,
      // 当前审批的记录
      currentApprovalRecord: {},
      // 选中的记录ID列表
      selectedApprovalIds: [],
      // 用户角色
      userRole: ''
    }
  },
  methods: {
    // 打开审批对话框
    openApprovalDialog(record) {
      this.currentApprovalRecord = record
      this.approvalDialogVisible = true
    },

    // 打开批量审批对话框
    openBatchApprovalDialog(selectedIds) {
      if (!selectedIds || selectedIds.length === 0) {
        this.$message.warning('请先选择要审批的记录')
        return
      }
      this.selectedApprovalIds = selectedIds
      this.batchApprovalDialogVisible = true
    },

    // 处理单个审批
    async handleApproval(approvalRequest, api) {
      try {
        const response = await api.approve(approvalRequest)
        if (approvalUtils.handleApprovalResponse(response)) {
          this.approvalDialogVisible = false
          this.refreshList()
        }
      } catch (error) {
        this.$message.error('审批失败：' + error.message)
      }
    },

    // 处理批量审批
    async handleBatchApproval(batchRequest, api) {
      try {
        const response = await api.batchApprove(batchRequest)
        if (approvalUtils.handleBatchApprovalResponse(response)) {
          this.batchApprovalDialogVisible = false
          this.selectedApprovalIds = []
          this.refreshList()
        }
      } catch (error) {
        this.$message.error('批量审批失败：' + error.message)
      }
    },

    // 刷新列表（需要在具体组件中实现）
    refreshList() {
      console.warn('refreshList method should be implemented in component')
    }
  }
}
