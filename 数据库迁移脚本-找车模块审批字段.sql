-- 找车模块统一审批流程数据库迁移脚本
-- 执行日期: 2025-08-01
-- 说明: 为vw_car_order_examine表添加统一审批流程所需的字段

-- 1. 检查表是否存在
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'vw_car_order_examine';

-- 2. 添加统一审批流程所需字段
-- 注意：如果字段已存在，请先检查并备份数据

-- 添加审批人姓名字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS approve_by VARCHAR(100) COMMENT '审批人姓名' AFTER reject_reason;

-- 添加审批人角色字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS approve_role VARCHAR(100) COMMENT '审批人角色' AFTER approve_by;

-- 添加申请时间字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS application_time DATETIME COMMENT '申请时间' AFTER approve_role;

-- 添加申请人字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS application_by VARCHAR(100) COMMENT '申请人' AFTER application_time;

-- 添加审批历史记录字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS approval_history TEXT COMMENT '审批历史记录' AFTER application_by;

-- 添加当前审批人字段
ALTER TABLE vw_car_order_examine 
ADD COLUMN IF NOT EXISTS current_approver VARCHAR(100) COMMENT '当前审批人' AFTER approval_history;

-- 3. 数据迁移和初始化
-- 将现有的examine_time数据迁移到application_time（如果需要）
UPDATE vw_car_order_examine 
SET application_time = examine_time 
WHERE application_time IS NULL AND examine_time IS NOT NULL;

-- 初始化现有记录的审批状态（如果需要）
-- 将原来的简单状态映射到新的统一审批状态
UPDATE vw_car_order_examine 
SET status = CASE 
    WHEN status = 0 THEN 0  -- 未审批 -> 通过
    WHEN status = 1 THEN 1  -- 已通过 -> 全部同意
    WHEN status = 2 THEN 2  -- 已拒绝 -> 已拒绝
    ELSE 3  -- 其他状态 -> 法诉主管审批
END
WHERE status IS NOT NULL;

-- 4. 创建索引以提高查询性能
-- 审批状态索引
CREATE INDEX IF NOT EXISTS idx_vw_car_order_examine_approval_status 
ON vw_car_order_examine(status);

-- 审批人索引
CREATE INDEX IF NOT EXISTS idx_vw_car_order_examine_approve_by 
ON vw_car_order_examine(approve_by);

-- 申请人索引
CREATE INDEX IF NOT EXISTS idx_vw_car_order_examine_application_by 
ON vw_car_order_examine(application_by);

-- 申请时间索引
CREATE INDEX IF NOT EXISTS idx_vw_car_order_examine_application_time 
ON vw_car_order_examine(application_time);

-- 5. 验证迁移结果
-- 检查新增字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE table_schema = DATABASE() 
    AND table_name = 'vw_car_order_examine'
    AND COLUMN_NAME IN ('approve_by', 'approve_role', 'application_time', 'application_by', 'approval_history', 'current_approver')
ORDER BY ORDINAL_POSITION;

-- 检查数据统计
SELECT 
    status,
    COUNT(*) as count,
    CASE 
        WHEN status = 0 THEN '通过'
        WHEN status = 1 THEN '全部同意'
        WHEN status = 2 THEN '已拒绝'
        WHEN status = 3 THEN '法诉主管审批'
        WHEN status = 4 THEN '总监审批'
        WHEN status = 5 THEN '财务主管/总监抄送'
        WHEN status = 6 THEN '总经理/董事长审批(抄送)'
        ELSE '未知状态'
    END as status_desc
FROM vw_car_order_examine 
GROUP BY status
ORDER BY status;

-- 6. 回滚脚本（如果需要回滚，请谨慎执行）
/*
-- 警告：以下是回滚脚本，执行前请确保已备份数据！

-- 删除新增的字段
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS current_approver;
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS approval_history;
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS application_by;
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS application_time;
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS approve_role;
ALTER TABLE vw_car_order_examine DROP COLUMN IF EXISTS approve_by;

-- 删除创建的索引
DROP INDEX IF EXISTS idx_vw_car_order_examine_application_time ON vw_car_order_examine;
DROP INDEX IF EXISTS idx_vw_car_order_examine_application_by ON vw_car_order_examine;
DROP INDEX IF EXISTS idx_vw_car_order_examine_approve_by ON vw_car_order_examine;
DROP INDEX IF EXISTS idx_vw_car_order_examine_approval_status ON vw_car_order_examine;
*/

-- 迁移完成提示
SELECT '找车模块统一审批流程数据库迁移完成！' as message;
