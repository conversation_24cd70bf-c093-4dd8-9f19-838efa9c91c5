package com.ruoyi.vw_car_order_examine.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseApprovalController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vw_car_order_examine.domain.VwCarOrderExamine;
import com.ruoyi.vw_car_order_examine.service.IVwCarOrderExamineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 找车费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/vw_car_order_examine/vw_car_order_examine")
public class VwCarOrderExamineController extends BaseApprovalController<VwCarOrderExamine, IVwCarOrderExamineService>
{
    @Autowired
    private IVwCarOrderExamineService vwCarOrderExamineService;

    @Override
    protected IVwCarOrderExamineService getService() {
        return vwCarOrderExamineService;
    }

    /**
     * 查询找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(VwCarOrderExamine vwCarOrderExamine)
    {
        startPage();
        List<VwCarOrderExamine> list = vwCarOrderExamineService.selectVwCarOrderExamineList(vwCarOrderExamine);
        return getDataTable(list);
    }

    /**
     * 导出找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:export')")
    @Log(title = "找车费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VwCarOrderExamine vwCarOrderExamine)
    {
        List<VwCarOrderExamine> list = vwCarOrderExamineService.selectVwCarOrderExamineList(vwCarOrderExamine);
        ExcelUtil<VwCarOrderExamine> util = new ExcelUtil<VwCarOrderExamine>(VwCarOrderExamine.class);
        util.exportExcel(response, list, "找车费用审批数据");
    }

    /**
     * 获取找车费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(vwCarOrderExamineService.selectVwCarOrderExamineById(id));
    }

    /**
     * 新增找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:add')")
    @Log(title = "找车费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VwCarOrderExamine vwCarOrderExamine)
    {
        return toAjax(vwCarOrderExamineService.insertVwCarOrderExamine(vwCarOrderExamine));
    }

    /**
     * 修改找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:edit')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VwCarOrderExamine vwCarOrderExamine)
    {
        return toAjax(vwCarOrderExamineService.updateVwCarOrderExamine(vwCarOrderExamine));
    }

    /**
     * 删除找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:remove')")
    @Log(title = "找车费用审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(vwCarOrderExamineService.deleteVwCarOrderExamineByIds(ids));
    }

    /**
     * 单个审批 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:approve')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approveRecord(@RequestBody ApprovalRequest request)
    {
        return super.approve(request);
    }

    /**
     * 批量审批 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:approve')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApproveRecords(@RequestBody BatchApprovalRequest request)
    {
        return super.batchApprove(request);
    }

    /**
     * 获取待审批列表 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:query')")
    @GetMapping("/pending")
    public AjaxResult getPendingList()
    {
        return super.getPendingApprovalList();
    }

    /**
     * 获取审批状态统计 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return super.getApprovalStatistics();
    }
}
