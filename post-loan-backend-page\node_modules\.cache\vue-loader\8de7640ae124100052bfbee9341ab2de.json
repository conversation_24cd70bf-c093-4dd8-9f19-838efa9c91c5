{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue", "mtime": 1753954068201}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICB0ZWFtVm1fY2FyX29yZGVyLA0KICBleGFtaW5lX29yZGVyLA0KICBhcHByb3ZlQ2FyT3JkZXJFeGFtaW5lLA0KICBsaXN0UGVuZGluZ0FwcHJvdmFsLA0KICBsaXN0VndfY2FyX29yZGVyX2V4YW1pbmUsDQogIGdldFZ3X2Nhcl9vcmRlcl9leGFtaW5lLA0KICBkZWxWd19jYXJfb3JkZXJfZXhhbWluZSwNCiAgYWRkVndfY2FyX29yZGVyX2V4YW1pbmUsDQogIHVwZGF0ZVZ3X2Nhcl9vcmRlcl9leGFtaW5lLA0KfSBmcm9tICdAL2FwaS92d19jYXJfb3JkZXJfZXhhbWluZS92d19jYXJfb3JkZXJfZXhhbWluZScNCmltcG9ydCB1c2VySW5mbyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy91c2VySW5mby52dWUnDQppbXBvcnQgY2FySW5mbyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9jYXJJbmZvLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnVndfY2FyX29yZGVyX2V4YW1pbmUnLA0KICBjb21wb25lbnRzOiB7DQogICAgdXNlckluZm8sDQogICAgY2FySW5mbywNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOaJvui9pui0ueeUqOWuoeaJueihqOagvOaVsOaNrg0KICAgICAgdndfY2FyX29yZGVyX2V4YW1pbmVMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDE1LA0KICAgICAgICB0ZWFtTmFtZTogbnVsbCwNCiAgICAgICAga2V5U3RhdHVzOiBudWxsLA0KICAgICAgICBvcmlnaW5hbGx5VGltZTogbnVsbCwNCiAgICAgICAgc3RhcnRUaW1lOiAnJywNCiAgICAgICAgZW5kVGltZTogJycsDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgcGxhdGVObzogbnVsbCwNCiAgICAgICAgamdOYW1lOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybTogew0KICAgICAgICBpZDogJycsDQogICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgbmV3U3RhdHVzOiBudWxsLA0KICAgICAgICByZWplY3RSZWFzb246IG51bGwsDQogICAgICAgIGN1c3RvbWVyTmFtZTogJycsDQogICAgICAgIGN1c3RvbWVySWQ6ICcnLA0KICAgICAgICBhcHBseUlkOiAnJywNCiAgICAgICAgcGxhdGVObzogJycsDQogICAgICAgIGpnTmFtZTogJycsDQogICAgICAgIHRlYW1OYW1lOiAnJywNCiAgICAgICAgYWxsb2NhdGlvblRpbWU6ICcnLA0KICAgICAgICBrZXlTdGF0dXM6ICcnLA0KICAgICAgICB0b3RhbENvc3Q6ICcnLA0KICAgICAgICBfcmVhZG9ubHk6IGZhbHNlLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgaWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAnJGNvbW1lbnTkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICB9LA0KICAgICAgamdOYW1lTGlzdDogWw0KICAgICAgICB7IGxhYmVsOiAnQeWFrOWPuCcsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICdC5YWs5Y+4JywgdmFsdWU6IDIgfSwNCiAgICAgIF0sDQogICAgICBrZXlTdGF0dXNMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflt7Lpgq7lr4QnLCB2YWx1ZTogMSB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5pS25ZueJywgdmFsdWU6IDIgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suW9kui/mCcsIHZhbHVlOiAzIH0sDQogICAgICBdLA0KICAgICAgdGVhbUxpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ0Hlm6LpmJ8nLCB2YWx1ZTogMSB9LA0KICAgICAgICB7IGxhYmVsOiAnQuWboumYnycsIHZhbHVlOiAyIH0sDQogICAgICBdLA0KICAgICAgY3VzdG9tZXJJbmZvOiB7IGN1c3RvbWVySWQ6ICcnLCBhcHBseUlkOiAnJyB9LA0KICAgICAgdXNlckluZm9WaXNpYmxlOiBmYWxzZSwNCiAgICAgIHBsYXRlTm86ICcnLA0KICAgICAgY2FySW5mb1Zpc2libGU6IGZhbHNlLA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFRlYW0oKQ0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmn6Xor6LlvZXljZXmuKDpgZPjgIHmib7ovablm6LpmJ8NCiAgICBnZXRUZWFtKCkgew0KICAgICAgdGVhbVZtX2Nhcl9vcmRlcigpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnRlYW1MaXN0ID0gcmVzcG9uc2UudGVhbQ0KICAgICAgICB0aGlzLmpnTmFtZUxpc3QgPSByZXNwb25zZS5vZmZpY2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5p+l6K+i5om+6L2m6LS555So5a6h5om55YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RWd19jYXJfb3JkZXJfZXhhbWluZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52d19jYXJfb3JkZXJfZXhhbWluZUxpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogJycsDQogICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgbmV3U3RhdHVzOiBudWxsLA0KICAgICAgICByZWplY3RSZWFzb246IG51bGwsDQogICAgICAgIGN1c3RvbWVyTmFtZTogJycsDQogICAgICAgIGN1c3RvbWVySWQ6ICcnLA0KICAgICAgICBhcHBseUlkOiAnJywNCiAgICAgICAgcGxhdGVObzogJycsDQogICAgICAgIGpnTmFtZTogJycsDQogICAgICAgIHRlYW1OYW1lOiAnJywNCiAgICAgICAgYWxsb2NhdGlvblRpbWU6ICcnLA0KICAgICAgICBrZXlTdGF0dXM6ICcnLA0KICAgICAgICB0b3RhbENvc3Q6ICcnLA0KICAgICAgICBfcmVhZG9ubHk6IGZhbHNlLA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMub3JpZ2luYWxseVRpbWUpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSB0aGlzLnF1ZXJ5UGFyYW1zLm9yaWdpbmFsbHlUaW1lWzBdDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IHRoaXMucXVlcnlQYXJhbXMub3JpZ2luYWxseVRpbWVbMV0NCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY3VzdG9tZXJOYW1lID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wbGF0ZU5vID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5qZ05hbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmtleVN0YXR1cyA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMudGVhbU5hbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9yaWdpbmFsbHlUaW1lID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBudWxsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqDmib7ovabotLnnlKjlrqHmibknDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgLy8gdGhpcy5yZXNldCgpDQogICAgICB0aGlzLmZvcm0uaWQgPSByb3cuaWQNCiAgICAgIHRoaXMuZm9ybS5yZWplY3RSZWFzb24gPSByb3cucmVqZWN0UmVhc29uDQogICAgICB0aGlzLmZvcm0uc3RhdHVzID0gcm93LnN0YXR1cw0KICAgICAgLy8gY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHMNCiAgICAgIC8vIGdldFZ3X2Nhcl9vcmRlcl9leGFtaW5lKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgLy8gdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KDQogICAgICAvLyB9KQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICfmib7ovabotLnnlKjlrqHmibknDQogICAgfSwNCiAgICBoYW5kbGVFeGFtaW5lKHJvdywgaXNEZXRhaWwgPSBmYWxzZSkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzdGF0dXM6IHJvdy5zdGF0dXMgfHwgMCwNCiAgICAgICAgbmV3U3RhdHVzOiBudWxsLA0KICAgICAgICByZWplY3RSZWFzb246IHJvdy5yZWplY3RSZWFzb24gfHwgbnVsbCwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiByb3cuY3VzdG9tZXJOYW1lIHx8ICcnLA0KICAgICAgICBjdXN0b21lcklkOiByb3cuY3VzdG9tZXJJZCB8fCAnJywNCiAgICAgICAgYXBwbHlJZDogcm93LmFwcGx5SWQgfHwgJycsDQogICAgICAgIHBsYXRlTm86IHJvdy5wbGF0ZU5vIHx8ICcnLA0KICAgICAgICBqZ05hbWU6IHJvdy5qZ05hbWUgfHwgcm93LmNoYW5uZWwgfHwgJycsDQogICAgICAgIHRlYW1OYW1lOiByb3cudGVhbU5hbWUgfHwgJycsDQogICAgICAgIGFsbG9jYXRpb25UaW1lOiByb3cuYWxsb2NhdGlvblRpbWUgfHwgJycsDQogICAgICAgIGtleVN0YXR1czogcm93LmtleVN0YXR1cyB8fCAnJywNCiAgICAgICAgdG90YWxDb3N0OiByb3cudG90YWxDb3N0IHx8ICcnLA0KICAgICAgICBfcmVhZG9ubHk6ICEhaXNEZXRhaWwsDQogICAgICB9DQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCiAgICAvLyDojrflj5bnirbmgIHmlofmnKwNCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAwOiAn5pyq5a6h5om5JywNCiAgICAgICAgMTogJ+WFqOmDqOmAmui/hycsDQogICAgICAgIDM6ICfms5Xor4nkuLvnrqHlrqHmibknLA0KICAgICAgICA0OiAn5oC755uR5a6h5om5JywNCiAgICAgICAgNTogJ+aAu+ebkeaKhOmAgScsDQogICAgICAgIDY6ICfmgLvnu4/nkIYv6JGj5LqL6ZW/5a6h5om5KOaKhOmAgSknLA0KICAgICAgICA3OiAn5bey5ouS57udJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6XnirbmgIEnDQogICAgfSwNCiAgICAvLyDojrflj5bkuIvkuIDkuKrnirbmgIENCiAgICBnZXROZXh0U3RhdHVzKGN1cnJlbnRTdGF0dXMpIHsNCiAgICAgIGNvbnN0IG5leHRTdGF0dXNNYXAgPSB7DQogICAgICAgIDA6IDMsICAvLyDmnKrlrqHmibkgLT4g5rOV6K+J5Li7566h5a6h5om5DQogICAgICAgIDM6IDQsICAvLyDms5Xor4nkuLvnrqHlrqHmibkgLT4g5oC755uR5a6h5om5DQogICAgICAgIDQ6IDUsICAvLyDmgLvnm5HlrqHmibkgLT4g5oC755uR5oqE6YCBDQogICAgICAgIDU6IDYsICAvLyDmgLvnm5HmioTpgIEgLT4g5oC757uP55CGL+iRo+S6i+mVv+WuoeaJuSjmioTpgIEpDQogICAgICAgIDY6IDEgICAvLyDmgLvnu4/nkIYv6JGj5LqL6ZW/5a6h5om5KOaKhOmAgSkgLT4g5YWo6YOo6YCa6L+HDQogICAgICB9DQogICAgICByZXR1cm4gbmV4dFN0YXR1c01hcFtjdXJyZW50U3RhdHVzXSB8fCBjdXJyZW50U3RhdHVzDQogICAgfSwNCiAgICAvLyDliKTmlq3mmK/lkKblj6/ku6XlrqHmibkNCiAgICBjYW5BcHByb3ZlKHJvdykgew0KICAgICAgLy8g5Y+q5pyJ5pyq5a6M5oiQ55qE54q25oCB5omN6IO95a6h5om5DQogICAgICByZXR1cm4gcm93LnN0YXR1cyAhPT0gMSAmJiByb3cuc3RhdHVzICE9PSA3DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0ubmV3U3RhdHVzKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nlrqHmibnnu5PmnpwnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmZvcm0ubmV3U3RhdHVzID09IDcgJiYgIXRoaXMuZm9ybS5yZWplY3RSZWFzb24pIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+i+k+WFpeaLkue7neWOn+WboCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDlh4blpIfmj5DkuqTmlbDmja4NCiAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgIGlkOiB0aGlzLmZvcm0uaWQsDQogICAgICAgIHN0YXR1czogdGhpcy5mb3JtLm5ld1N0YXR1cywNCiAgICAgICAgcmVqZWN0UmVhc29uOiB0aGlzLmZvcm0ucmVqZWN0UmVhc29uDQogICAgICB9DQoNCiAgICAgIC8vIOS9v+eUqOaWsOeahOWuoeaJuea1geeoi+aOpeWPow0KICAgICAgYXBwcm92ZUNhck9yZGVyRXhhbWluZShzdWJtaXREYXRhKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5a6h5om55oiQ5YqfJykNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoZXJyb3IubXNnIHx8ICflrqHmibnlpLHotKUnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmib7ovabotLnnlKjlrqHmibnnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBkZWxWd19jYXJfb3JkZXJfZXhhbWluZShpZHMpDQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAndndfY2FyX29yZGVyX2V4YW1pbmUvdndfY2FyX29yZGVyX2V4YW1pbmUvZXhwb3J0JywNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgIH0sDQogICAgICAgIGB2d19jYXJfb3JkZXJfZXhhbWluZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKQ0KICAgIH0sDQogICAgb3BlblVzZXJJbmZvKGN1c3RvbWVySW5mbykgew0KICAgICAgY29uc29sZS5sb2coJ+eCueWHu+WuouaIt+S/oeaBrzonLCBjdXN0b21lckluZm8pDQogICAgICBpZiAoIWN1c3RvbWVySW5mby5jdXN0b21lcklkIHx8ICFjdXN0b21lckluZm8uYXBwbHlJZCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5a6i5oi35L+h5oGv5LiN5a6M5pW077yM5peg5rOV5p+l55yL6K+m5oOFJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmN1c3RvbWVySW5mbyA9IGN1c3RvbWVySW5mbw0KICAgICAgdGhpcy51c2VySW5mb1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBvcGVuQ2FySW5mbyhwbGF0ZU5vKSB7DQogICAgICB0aGlzLnBsYXRlTm8gPSBwbGF0ZU5vDQogICAgICB0aGlzLmNhckluZm9WaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogIH0sDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vw_car_order_examine/vw_car_order_examine", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录入渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"请输入车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-input v-model=\"queryParams.teamName\" placeholder=\"找车团队\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.originallyTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.customerId && scope.row.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ scope.row.customerName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" />\r\n      <!-- 出单渠道 -->\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\"></el-table-column>\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"接单团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"入库时间\" align=\"center\" prop=\"inboundTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"找车佣金\" align=\"center\" prop=\"locatingCommission\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalCost\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getStatusText(scope.row.status) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"canApprove(scope.row)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleExamine(scope.row)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            审批\r\n          </el-button>\r\n          <el-button\r\n            v-if=\"scope.row.status == 1 || scope.row.status == 7\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleExamine(scope.row, true)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            查看\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改找车费用审批对话框 -->\r\n    <el-dialog :title=\"form._readonly ? '详情' : '审批'\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border style=\"margin-bottom: 20px\">\r\n        <el-descriptions-item label=\"贷款人\">\r\n          <el-button\r\n            v-if=\"form.customerId && form.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: form.customerId, applyId: form.applyId })\">\r\n            {{ form.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ form.customerName }}</span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"车牌号\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(form.plateNo)\">{{ form.plateNo }}</el-button>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">{{ form.jgName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"接单团队\">{{ form.teamName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"派单时间\">{{ form.allocationTime }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"钥匙状态\">\r\n          <span>{{ form.keyStatus == 1 ? '已邮寄' : form.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"合计费用\">{{ form.totalCost }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n        <template v-if=\"form._readonly && form.status === 1\">\r\n          <div style=\"color: #67c23a; font-size: 18px; margin-bottom: 16px;\">全部通过</div>\r\n        </template>\r\n        <template v-else-if=\"form._readonly && form.status === 7\">\r\n          <div style=\"color: #f56c6c; font-size: 18px; margin-bottom: 16px;\">已拒绝</div>\r\n        </template>\r\n        <template v-else-if=\"form._readonly\">\r\n          <div style=\"color: #409eff; font-size: 18px; margin-bottom: 16px;\">{{ getStatusText(form.status) }}</div>\r\n        </template>\r\n        <template v-if=\"!form._readonly\">\r\n          <el-form-item label=\"当前状态\">\r\n            <span>{{ getStatusText(form.status) }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"审批操作\">\r\n            <el-radio-group v-model=\"form.newStatus\">\r\n              <el-radio :label=\"getNextStatus(form.status)\">通过</el-radio>\r\n              <el-radio :label=\"7\">拒绝</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"form.newStatus == 7\" label=\"拒绝原因\">\r\n            <el-input type=\"textarea\" :rows=\"2\" placeholder=\"请输入拒绝原因\" v-model=\"form.rejectReason\"></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!form._readonly\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">{{ form._readonly ? '关 闭' : '取 消' }}</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  teamVm_car_order,\r\n  examine_order,\r\n  approveCarOrderExamine,\r\n  listPendingApproval,\r\n  listVw_car_order_examine,\r\n  getVw_car_order_examine,\r\n  delVw_car_order_examine,\r\n  addVw_car_order_examine,\r\n  updateVw_car_order_examine,\r\n} from '@/api/vw_car_order_examine/vw_car_order_examine'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\n\r\nexport default {\r\n  name: 'Vw_car_order_examine',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      vw_car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        teamName: null,\r\n        keyStatus: null,\r\n        originallyTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        customerName: null,\r\n        plateNo: null,\r\n        jgName: null,\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalCost: '',\r\n        _readonly: false,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      teamList: [\r\n        { label: 'A团队', value: 1 },\r\n        { label: 'B团队', value: 2 },\r\n      ],\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeam()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 查询录单渠道、找车团队\r\n    getTeam() {\r\n      teamVm_car_order().then(response => {\r\n        this.teamList = response.team\r\n        this.jgNameList = response.office\r\n      })\r\n    },\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_car_order_examine(this.queryParams).then(response => {\r\n        this.vw_car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalCost: '',\r\n        _readonly: false,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加找车费用审批'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // this.reset()\r\n      this.form.id = row.id\r\n      this.form.rejectReason = row.rejectReason\r\n      this.form.status = row.status\r\n      // const id = row.id || this.ids\r\n      // getVw_car_order_examine(id).then(response => {\r\n      //   // this.form = response.data\r\n\r\n      // })\r\n      this.open = true\r\n      this.title = '找车费用审批'\r\n    },\r\n    handleExamine(row, isDetail = false) {\r\n      this.form = {\r\n        id: row.id,\r\n        status: row.status || 0,\r\n        newStatus: null,\r\n        rejectReason: row.rejectReason || null,\r\n        customerName: row.customerName || '',\r\n        customerId: row.customerId || '',\r\n        applyId: row.applyId || '',\r\n        plateNo: row.plateNo || '',\r\n        jgName: row.jgName || row.channel || '',\r\n        teamName: row.teamName || '',\r\n        allocationTime: row.allocationTime || '',\r\n        keyStatus: row.keyStatus || '',\r\n        totalCost: row.totalCost || '',\r\n        _readonly: !!isDetail,\r\n      }\r\n      this.open = true\r\n    },\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        3: '法诉主管审批',\r\n        4: '总监审批',\r\n        5: '总监抄送',\r\n        6: '总经理/董事长审批(抄送)',\r\n        7: '已拒绝'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n    // 获取下一个状态\r\n    getNextStatus(currentStatus) {\r\n      const nextStatusMap = {\r\n        0: 3,  // 未审批 -> 法诉主管审批\r\n        3: 4,  // 法诉主管审批 -> 总监审批\r\n        4: 5,  // 总监审批 -> 总监抄送\r\n        5: 6,  // 总监抄送 -> 总经理/董事长审批(抄送)\r\n        6: 1   // 总经理/董事长审批(抄送) -> 全部通过\r\n      }\r\n      return nextStatusMap[currentStatus] || currentStatus\r\n    },\r\n    // 判断是否可以审批\r\n    canApprove(row) {\r\n      // 只有未完成的状态才能审批\r\n      return row.status !== 1 && row.status !== 7\r\n    },\r\n    submitForm() {\r\n      if (!this.form.newStatus) {\r\n        this.$modal.msgError('请选择审批结果')\r\n        return\r\n      }\r\n      if (this.form.newStatus == 7 && !this.form.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      // 准备提交数据\r\n      const submitData = {\r\n        id: this.form.id,\r\n        status: this.form.newStatus,\r\n        rejectReason: this.form.rejectReason\r\n      }\r\n\r\n      // 使用新的审批流程接口\r\n      approveCarOrderExamine(submitData).then(() => {\r\n        this.$modal.msgSuccess('审批成功')\r\n        this.open = false\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError(error.msg || '审批失败')\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_car_order_examine(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_car_order_examine/vw_car_order_examine/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_car_order_examine_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      console.log('点击客户信息:', customerInfo)\r\n      if (!customerInfo.customerId || !customerInfo.applyId) {\r\n        this.$modal.msgError('客户信息不完整，无法查看详情')\r\n        return\r\n      }\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n"]}]}