package com.ruoyi.vw_car_order_examine.service;

import java.util.List;
import com.ruoyi.vw_car_order_examine.domain.VwCarOrderExamine;
import com.ruoyi.common.service.IBaseApprovalService;

/**
 * 找车费用审批Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IVwCarOrderExamineService extends IBaseApprovalService<VwCarOrderExamine>
{
    /**
     * 查询找车费用审批
     * 
     * @param id 找车费用审批主键
     * @return 找车费用审批
     */
    public VwCarOrderExamine selectVwCarOrderExamineById(String id);

    /**
     * 查询找车费用审批列表
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 找车费用审批集合
     */
    public List<VwCarOrderExamine> selectVwCarOrderExamineList(VwCarOrderExamine vwCarOrderExamine);

    /**
     * 新增找车费用审批
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 结果
     */
    public int insertVwCarOrderExamine(VwCarOrderExamine vwCarOrderExamine);

    /**
     * 修改找车费用审批
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 结果
     */
    public int updateVwCarOrderExamine(VwCarOrderExamine vwCarOrderExamine);

    /**
     * 批量删除找车费用审批
     * 
     * @param ids 需要删除的找车费用审批主键集合
     * @return 结果
     */
    public int deleteVwCarOrderExamineByIds(String[] ids);

    /**
     * 删除找车费用审批信息
     * 
     * @param id 找车费用审批主键
     * @return 结果
     */
    public int deleteVwCarOrderExamineById(String id);
}
