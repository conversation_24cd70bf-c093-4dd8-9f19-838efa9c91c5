/**
 * 统一审批流程常量定义
 * <AUTHOR>
 * @date 2025-08-01
 */

// 审批状态常量
export const APPROVAL_STATUS = {
  PENDING: 0,           // 通过
  APPROVED: 1,          // 全部同意
  REJECTED: 2,          // 已拒绝
  LEGAL_SUPERVISOR: 3,  // 法诉主管审批
  DIRECTOR: 4,          // 总监审批
  FINANCE_CC: 5,        // 财务主管/总监抄送
  GENERAL_MANAGER: 6    // 总经理/董事长审批(抄送)
}

// 审批状态描述映射
export const APPROVAL_STATUS_MAP = {
  [APPROVAL_STATUS.PENDING]: '通过',
  [APPROVAL_STATUS.APPROVED]: '全部同意',
  [APPROVAL_STATUS.REJECTED]: '已拒绝',
  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: '法诉主管审批',
  [APPROVAL_STATUS.DIRECTOR]: '总监审批',
  [APPROVAL_STATUS.FINANCE_CC]: '财务主管/总监抄送',
  [APPROVAL_STATUS.GENERAL_MANAGER]: '总经理/董事长审批(抄送)'
}

// 审批动作常量
export const APPROVAL_ACTION = {
  APPROVE: 'approve',   // 通过
  REJECT: 'reject'      // 拒绝
}

// 审批动作描述映射
export const APPROVAL_ACTION_MAP = {
  [APPROVAL_ACTION.APPROVE]: '通过',
  [APPROVAL_ACTION.REJECT]: '拒绝'
}

// 审批状态颜色映射（用于前端显示）
export const APPROVAL_STATUS_COLOR = {
  [APPROVAL_STATUS.PENDING]: 'info',
  [APPROVAL_STATUS.APPROVED]: 'success',
  [APPROVAL_STATUS.REJECTED]: 'danger',
  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: 'warning',
  [APPROVAL_STATUS.DIRECTOR]: 'warning',
  [APPROVAL_STATUS.FINANCE_CC]: 'warning',
  [APPROVAL_STATUS.GENERAL_MANAGER]: 'warning'
}

// 获取审批状态描述
export function getApprovalStatusText(status) {
  return APPROVAL_STATUS_MAP[status] || '未知状态'
}

// 获取审批状态颜色
export function getApprovalStatusColor(status) {
  return APPROVAL_STATUS_COLOR[status] || 'info'
}

// 检查是否为最终状态
export function isFinalStatus(status) {
  return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED
}

// 获取下一个审批状态
export function getNextApprovalStatus(currentStatus) {
  switch (currentStatus) {
    case APPROVAL_STATUS.PENDING:
      return APPROVAL_STATUS.LEGAL_SUPERVISOR
    case APPROVAL_STATUS.LEGAL_SUPERVISOR:
      return APPROVAL_STATUS.DIRECTOR
    case APPROVAL_STATUS.DIRECTOR:
      return APPROVAL_STATUS.FINANCE_CC
    case APPROVAL_STATUS.FINANCE_CC:
      return APPROVAL_STATUS.GENERAL_MANAGER
    case APPROVAL_STATUS.GENERAL_MANAGER:
      return APPROVAL_STATUS.APPROVED
    default:
      return currentStatus
  }
}

// 检查用户是否可以审批当前状态
export function canApprove(currentStatus, userRole) {
  switch (currentStatus) {
    case APPROVAL_STATUS.PENDING:
      return userRole === '法诉主管'
    case APPROVAL_STATUS.LEGAL_SUPERVISOR:
      return userRole === '总监'
    case APPROVAL_STATUS.DIRECTOR:
      return userRole === '财务主管' || userRole === '财务总监'
    case APPROVAL_STATUS.FINANCE_CC:
      return userRole === '总经理' || userRole === '董事长'
    default:
      return false
  }
}

// 审批流程步骤定义（用于进度条显示）
export const APPROVAL_STEPS = [
  {
    title: '法诉主管审批',
    status: APPROVAL_STATUS.LEGAL_SUPERVISOR,
    description: '法诉主管进行初步审批'
  },
  {
    title: '总监审批',
    status: APPROVAL_STATUS.DIRECTOR,
    description: '总监进行二级审批'
  },
  {
    title: '财务主管/总监抄送',
    status: APPROVAL_STATUS.FINANCE_CC,
    description: '财务部门确认'
  },
  {
    title: '总经理/董事长审批',
    status: APPROVAL_STATUS.GENERAL_MANAGER,
    description: '最高级别审批'
  },
  {
    title: '审批完成',
    status: APPROVAL_STATUS.APPROVED,
    description: '所有审批流程完成'
  }
]

// 获取当前审批步骤索引
export function getCurrentStepIndex(status) {
  return APPROVAL_STEPS.findIndex(step => step.status === status)
}
