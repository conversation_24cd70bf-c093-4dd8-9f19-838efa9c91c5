{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\BatchApprovalDialog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\BatchApprovalDialog\\index.vue", "mtime": 1754013097602}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/BatchApprovalDialog", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量审批\"\n    :visible.sync=\"visible\"\n    width=\"500px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"batch-approval-dialog\">\n      <!-- 选中记录信息 -->\n      <div class=\"selected-info\">\n        <el-alert\n          :title=\"`已选择 ${selectedCount} 条记录进行批量审批`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n\n      <!-- 批量审批表单 -->\n      <el-form ref=\"batchForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">批量通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">批量拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 警告提示 -->\n      <div class=\"warning-tip\" v-if=\"form.approvalAction\">\n        <el-alert\n          :title=\"getWarningText()\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_ACTION\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'BatchApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 选中的记录数量\n    selectedCount: {\n      type: Number,\n      default: 0\n    },\n    // 选中的记录ID列表\n    selectedIds: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      APPROVAL_ACTION,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.batchForm) {\n          this.$refs.batchForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 获取警告提示文本\n    getWarningText() {\n      if (this.form.approvalAction === APPROVAL_ACTION.APPROVE) {\n        return `确认要批量通过这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      } else if (this.form.approvalAction === APPROVAL_ACTION.REJECT) {\n        return `确认要批量拒绝这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      }\n      return ''\n    },\n\n    // 提交批量审批\n    handleSubmit() {\n      if (this.selectedIds.length === 0) {\n        this.$message.error('请先选择要审批的记录')\n        return\n      }\n\n      this.$refs.batchForm.validate((valid) => {\n        if (valid) {\n          this.loading = true\n          const batchRequest = {\n            ids: this.selectedIds,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('batch-approve', batchRequest)\n        }\n      })\n    },\n\n    // 批量审批完成回调\n    onBatchApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 批量审批失败回调\n    onBatchApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.batch-approval-dialog {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.selected-info {\n  margin-bottom: 20px;\n}\n\n.warning-tip {\n  margin-top: 20px;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}