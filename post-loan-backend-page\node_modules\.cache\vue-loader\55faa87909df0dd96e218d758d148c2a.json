{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vm_car_order\\vm_car_order\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vm_car_order\\vm_car_order\\index.vue", "mtime": 1753946632623}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0Vm1fY2FyX29yZGVyLA0KICB0ZWFtVm1fY2FyX29yZGVyLA0KICBnZXRWbV9jYXJfb3JkZXIsDQogIGRlbFZtX2Nhcl9vcmRlciwNCiAgYWRkVm1fY2FyX29yZGVyLA0KICB1cGRhdGVWbV9jYXJfb3JkZXIsDQogIFJldm9rZVZtX2Nhcl9vcmRlciwNCiAgc3VibWl0Q2FyRmluZENvc3QsDQogIG1haWxLZXksDQp9IGZyb20gJ0AvYXBpL3ZtX2Nhcl9vcmRlci92bV9jYXJfb3JkZXInDQppbXBvcnQgeyBsaXN0Q2FyX3RlYW0gfSBmcm9tICdAL2FwaS9jYXJfdGVhbS9jYXJfdGVhbScNCmltcG9ydCBhcmVhTGlzdCBmcm9tICcuLi8uLi8uLi9hc3NldHMvYXJlYS5qc29uJw0KaW1wb3J0IHVzZXJJbmZvIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL3VzZXJJbmZvLnZ1ZScNCmltcG9ydCBjYXJJbmZvIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL2NhckluZm8udnVlJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnVm1fY2FyX29yZGVyJywNCiAgY29tcG9uZW50czogew0KICAgIHVzZXJJbmZvLA0KICAgIGNhckluZm8sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyBWSUVX6KGo5qC85pWw5o2uDQogICAgICB2bV9jYXJfb3JkZXJMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDE1LA0KICAgICAgICBjdXN0b21lck5hbWU6ICcnLA0KICAgICAgICBwbGF0ZU5vOiAnJywNCiAgICAgICAgamdOYW1lOiAnJywNCiAgICAgICAgZ2FyYWdlTmFtZTogJycsDQogICAgICAgIGtleVN0YXR1czogJycsDQogICAgICAgIHN0YXR1czogJycsDQogICAgICAgIHRlYW1OYW1lOiAnJywNCiAgICAgICAgc3RhcnRUaW1lOiAnJywNCiAgICAgICAgZW5kVGltZTogJycsDQogICAgICAgIG9yaWdpbmFsbHlUaW1lOiAnJywNCiAgICAgIH0sDQogICAgICBqZ05hbWVMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICdB5YWs5Y+4JywgdmFsdWU6IDEgfSwNCiAgICAgICAgeyBsYWJlbDogJ0Llhazlj7gnLCB2YWx1ZTogMiB9LA0KICAgICAgXSwNCiAgICAgIGtleVN0YXR1c0xpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+W3sumCruWvhCcsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7LmlLblm54nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5b2S6L+YJywgdmFsdWU6IDMgfSwNCiAgICAgIF0sDQogICAgICBzdGF0dXNMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflj5HotbforqLljZUnLCB2YWx1ZTogMCB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5YiG6YWNJywgdmFsdWU6IDEgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suWujOaIkCcsIHZhbHVlOiAyIH0sDQogICAgICAgIHsgbGFiZWw6ICfmnKrlrozmiJAnLCB2YWx1ZTogMyB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5pKk6ZSAJywgdmFsdWU6IDQgfSwNCiAgICAgIF0sDQogICAgICB0ZWFtTGlzdDogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBrZXlQcm92aW5jZTogJycsDQogICAgICAgIGtleUNpdHk6ICcnLA0KICAgICAgICBrZXlCb3JvdWdoOiAnJywNCiAgICAgICAga2V5QWRkcmVzczogJycsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBrZXlQcm92aW5jZTogJycsDQogICAgICAgIGtleUNpdHk6ICcnLA0KICAgICAgICBrZXlCb3JvdWdoOiAnJywNCiAgICAgICAga2V5QWRkcmVzczogJycsDQogICAgICB9LA0KICAgICAgcHJvdmluY2VMaXN0OiBhcmVhTGlzdCwNCiAgICAgIGNpdHlMaXN0OiBbXSwNCiAgICAgIGRpc3RyaWN0TGlzdDogW10sDQogICAgICByZXZva2VMaXN0OiB7DQogICAgICAgIGlkOiAnJywNCiAgICAgICAgc3RhdHVzOiA0LA0KICAgICAgfSwNCiAgICAgIGN1c3RvbWVySW5mbzogeyBjdXN0b21lcklkOiAnJywgYXBwbHlJZDogJycgfSwNCiAgICAgIHVzZXJJbmZvVmlzaWJsZTogZmFsc2UsDQogICAgICBwbGF0ZU5vOiAnJywNCiAgICAgIGNhckluZm9WaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOaJvui9pui0ueeUqOebuOWFsw0KICAgICAgY29zdERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY29zdEZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBhcHBseU5vOiAnJywNCiAgICAgICAgbG9hbklkOiAnJywNCiAgICAgICAgdHJhbnNwb3J0YXRpb25GZWU6IDAsDQogICAgICAgIHRvd2luZ0ZlZTogMCwNCiAgICAgICAgdHJhY2tlckluc3RhbGxhdGlvbkZlZTogMCwNCiAgICAgICAgb3RoZXJSZWltYnVyc2VtZW50OiAwDQogICAgICB9LA0KICAgICAgY29zdFJ1bGVzOiB7DQogICAgICAgIHRyYW5zcG9ydGF0aW9uRmVlOiBbDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLCBtZXNzYWdlOiAn5L2j6YeR5LiN6IO95bCP5LqOMCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIHRvd2luZ0ZlZTogWw0KICAgICAgICAgIHsgdHlwZTogJ251bWJlcicsIG1pbjogMCwgbWVzc2FnZTogJ+aLlui9pui0ueS4jeiDveWwj+S6jjAnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICB0cmFja2VySW5zdGFsbGF0aW9uRmVlOiBbDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLCBtZXNzYWdlOiAn6LS05py66LS55LiN6IO95bCP5LqOMCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIG90aGVyUmVpbWJ1cnNlbWVudDogWw0KICAgICAgICAgIHsgdHlwZTogJ251bWJlcicsIG1pbjogMCwgbWVzc2FnZTogJ+WFtuS7luaKpemUgOS4jeiDveWwj+S6jjAnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDorqHnrpfmgLvotLnnlKgNCiAgICB0b3RhbENvc3QoKSB7DQogICAgICBjb25zdCB0cmFuc3BvcnRhdGlvbkZlZSA9IHRoaXMuY29zdEZvcm0udHJhbnNwb3J0YXRpb25GZWUgfHwgMA0KICAgICAgY29uc3QgdG93aW5nRmVlID0gdGhpcy5jb3N0Rm9ybS50b3dpbmdGZWUgfHwgMA0KICAgICAgY29uc3QgdHJhY2tlckluc3RhbGxhdGlvbkZlZSA9IHRoaXMuY29zdEZvcm0udHJhY2tlckluc3RhbGxhdGlvbkZlZSB8fCAwDQogICAgICBjb25zdCBvdGhlclJlaW1idXJzZW1lbnQgPSB0aGlzLmNvc3RGb3JtLm90aGVyUmVpbWJ1cnNlbWVudCB8fCAwDQogICAgICByZXR1cm4gKHRyYW5zcG9ydGF0aW9uRmVlICsgdG93aW5nRmVlICsgdHJhY2tlckluc3RhbGxhdGlvbkZlZSArIG90aGVyUmVpbWJ1cnNlbWVudCkudG9GaXhlZCgyKQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFRlYW1MaXN0KCkNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlQ2hhbmdlKHZhbHVlKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmpnTmFtZSA9IHZhbHVlDQogICAgfSwNCiAgICBwcm92aW5jZUNoYW5nZShpdGVtKSB7DQogICAgICB0aGlzLmZvcm0ua2V5UHJvdmluY2UgPSBpdGVtLm5hbWUNCiAgICAgIHRoaXMuY2l0eUxpc3QgPSBpdGVtLmNoaWxkcmVuDQogICAgfSwNCiAgICBjaXR5Q2hhbmdlKGl0ZW0pIHsNCiAgICAgIHRoaXMuZm9ybS5rZXlDaXR5ID0gaXRlbS5uYW1lDQogICAgICB0aGlzLmRpc3RyaWN0TGlzdCA9IGl0ZW0uY2hpbGRyZW4NCiAgICB9LA0KICAgIGRpc3RyaWN0Q2hhbmdlKGl0ZW0pIHsNCiAgICAgIHRoaXMuZm9ybS5rZXlCb3JvdWdoID0gaXRlbS5uYW1lDQogICAgfSwNCiAgICBhc3luYyBnZXRUZWFtTGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGxpc3RDYXJfdGVhbSgpDQogICAgICAgIHRoaXMudGVhbUxpc3QgPSAocmVzLnJvd3MgfHwgW10pLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgbGFiZWw6IGl0ZW0udGVhbU5hbWUsDQogICAgICAgICAgdmFsdWU6IGl0ZW0udGVhbU5hbWUNCiAgICAgICAgfSkpDQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMudGVhbUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivolZJRVfliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGlzdFZtX2Nhcl9vcmRlcih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52bV9jYXJfb3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBrZXlQcm92aW5jZTogJycsDQogICAgICAgIGtleUNpdHk6ICcnLA0KICAgICAgICBrZXlCb3JvdWdoOiAnJywNCiAgICAgICAga2V5QWRkcmVzczogJycsDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5vcmlnaW5hbGx5VGltZSkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IHRoaXMucXVlcnlQYXJhbXMub3JpZ2luYWxseVRpbWVbMF0NCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gdGhpcy5xdWVyeVBhcmFtcy5vcmlnaW5hbGx5VGltZVsxXQ0KICAgICAgfQ0KICAgICAgLy8gZGVsZXRlIHRoaXMucXVlcnlQYXJhbXMub3JpZ2luYWxseVRpbWUNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY3VzdG9tZXJOYW1lID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wbGF0ZU5vID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5qZ05hbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmdhcmFnZU5hbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmtleVN0YXR1cyA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhdHVzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb2xsZWN0aW9uTWV0aG9kID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy50ZWFtTmFtZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JpZ2luYWxseVRpbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IG51bGwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgcmVzdFNlYXJjaCgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7DQogICAgICAgIGN1c3RvbWVyTmFtZTogJycsDQogICAgICAgIHBsYXRlTm86ICcnLA0KICAgICAgICBqZ05hbWU6ICcnLA0KICAgICAgICBnYXJhZ2VJZDogJycsDQogICAgICAgIGtleVN0YXR1czogJycsDQogICAgICAgIHN0YXR1czogJycsDQogICAgICAgIHRlYW1OYW1lOiAnJywNCiAgICAgICAgc3RhcnRUaW1lOiAnJywNCiAgICAgICAgZW5kVGltZTogJycsDQogICAgICAgIG9yaWdpbmFsbHlUaW1lOiAnJywNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqBWSUVXJw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5mb3JtLmlkID0gcm93LmlkDQogICAgICB0aGlzLmZvcm0ua2V5UHJvdmluY2UgPSByb3cua2V5UHJvdmluY2UNCiAgICAgIHRoaXMuZm9ybS5rZXlDaXR5ID0gcm93LmtleUNpdHkNCiAgICAgIHRoaXMuZm9ybS5rZXlCb3JvdWdoID0gcm93LmtleUJvcm91Z2gNCiAgICAgIHRoaXMuZm9ybS5rZXlBZGRyZXNzID0gcm93LmtleUFkZHJlc3MNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRWbV9jYXJfb3JkZXIoaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAvLyB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICfpgq7lr4TpkqXljJknDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAvLyDpgq7lr4TpkqXljJnpgLvovpENCiAgICAgIG1haWxLZXkodGhpcy5mb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn6ZKl5YyZ6YKu5a+E5oiQ5YqfJykNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+mCruWvhOWksei0pe+8micgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpDQogICAgICB9KQ0KICAgICAgLy8gdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgIC8vICAgaWYgKHZhbGlkKSB7DQogICAgICAvLyAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAvLyAgICAgICB1cGRhdGVWbV9jYXJfb3JkZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgIC8vICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIC8vICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIC8vICAgICAgIH0pDQogICAgICAvLyAgICAgfSBlbHNlIHsNCiAgICAgIC8vICAgICAgIGFkZFZtX2Nhcl9vcmRlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgLy8gICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgLy8gICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgLy8gICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgLy8gICAgICAgfSkNCiAgICAgIC8vICAgICB9DQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXZva2Uocm93KSB7DQogICAgICBjb25zb2xlLmxvZygnMTExMScpDQogICAgICB0aGlzLnJldm9rZUxpc3QuaWQgPSByb3cuaWQNCiAgICAgIHZhciBkYXRhID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzdGF0dXM6IDQsDQogICAgICB9DQogICAgICAvLyBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTmkqTplIDvvJ8nKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgcmV0dXJuIFJldm9rZVZtX2Nhcl9vcmRlcihkYXRhKQ0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmkqTplIDmiJDlip8nKQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5pKk6ZSA57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsVm1fY2FyX29yZGVyKGlkcykNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5pKk6ZSA5oiQ5YqfJykNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsgfSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAndm1fY2FyX29yZGVyL3ZtX2Nhcl9vcmRlci9leHBvcnQnLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYHZtX2Nhcl9vcmRlcl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKQ0KICAgIH0sDQogICAgb3BlblVzZXJJbmZvKGN1c3RvbWVySW5mbykgew0KICAgICAgdGhpcy5jdXN0b21lckluZm8gPSBjdXN0b21lckluZm8NCiAgICAgIHRoaXMudXNlckluZm9WaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgb3BlbkNhckluZm8ocGxhdGVObykgew0KICAgICAgdGhpcy5wbGF0ZU5vID0gcGxhdGVObw0KICAgICAgdGhpcy5jYXJJbmZvVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOWkhOeQhuaPkOS6pOaJvui9pui0ueeUqA0KICAgIGhhbmRsZVN1Ym1pdENvc3Qocm93KSB7DQogICAgICB0aGlzLnJlc2V0Q29zdEZvcm0oKQ0KICAgICAgdGhpcy5jb3N0Rm9ybS5pZCA9IHJvdy5pZA0KICAgICAgdGhpcy5jb3N0Rm9ybS5hcHBseU5vID0gcm93LmFwcGx5Tm8NCiAgICAgIHRoaXMuY29zdEZvcm0ubG9hbklkID0gcm93LmxvYW5JZA0KICAgICAgdGhpcy5jb3N0RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOmHjee9ruaJvui9pui0ueeUqOihqOWNlQ0KICAgIHJlc2V0Q29zdEZvcm0oKSB7DQogICAgICB0aGlzLmNvc3RGb3JtID0gew0KICAgICAgICBpZDogJycsDQogICAgICAgIGFwcGx5Tm86ICcnLA0KICAgICAgICBsb2FuSWQ6ICcnLA0KICAgICAgICB0cmFuc3BvcnRhdGlvbkZlZTogMCwNCiAgICAgICAgdG93aW5nRmVlOiAwLA0KICAgICAgICB0cmFja2VySW5zdGFsbGF0aW9uRmVlOiAwLA0KICAgICAgICBvdGhlclJlaW1idXJzZW1lbnQ6IDANCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLiRyZWZzLmNvc3RGb3JtKSB7DQogICAgICAgIHRoaXMuJHJlZnMuY29zdEZvcm0ucmVzZXRGaWVsZHMoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5om+6L2m6LS555So5o+Q5LqkDQogICAgY2FuY2VsQ29zdCgpIHsNCiAgICAgIHRoaXMuY29zdERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldENvc3RGb3JtKCkNCiAgICB9LA0KICAgIC8vIOaPkOS6pOaJvui9pui0ueeUqOihqOWNlQ0KICAgIHN1Ym1pdENvc3RGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy5jb3N0Rm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOiwg+eUqEFQSeaPkOS6pOaJvui9pui0ueeUqA0KICAgICAgICAgIHN1Ym1pdENhckZpbmRDb3N0KHRoaXMuY29zdEZvcm0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5om+6L2m6LS555So5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICAgIHRoaXMuY29zdERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5yZXNldENvc3RGb3JtKCkNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vm_car_order/vm_car_order", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择订单状态\" clearable>\r\n          <el-option v-for=\"dict in statusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-select v-model=\"queryParams.teamName\" placeholder=\"请选择找车团队\" clearable>\r\n          <el-option v-for=\"item in teamList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker v-model=\"queryParams.originallyTime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row> -->\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vm_car_orderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyNo })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" width=\"130\" />\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" width=\"130\" />\r\n      <el-table-column label=\"逾期状态\" align=\"center\" prop=\"slippageStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.slippageStatus != null\">\r\n            {{\r\n              scope.row.slippageStatus == 1\r\n                ? '提醒'\r\n                : scope.row.slippageStatus == 2\r\n                  ? '电催'\r\n                  : scope.row.slippageStatus == 3\r\n                    ? '上访'\r\n                    : scope.row.slippageStatus == 4\r\n                      ? '逾期30-60'\r\n                      : '逾期60+'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"欠款金额\" align=\"center\" prop=\"overdueAmt\" width=\"130\" />\r\n      <el-table-column label=\"派单员\" align=\"center\" prop=\"dispatcher\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{ scope.row.dispatcher === 1 ? '贷后文员' : scope.row.dispatcher === 2 ? '法诉文员' : '' }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"业务员\" align=\"center\" prop=\"nickName\" />\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆位置\" align=\"center\" prop=\"carDetailAddress\" />\r\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"carStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.carStatus == 1\r\n                ? '省内正常行驶'\r\n                : scope.row.carStatus == 2\r\n                  ? '省外正常行驶'\r\n                  : scope.row.carStatus == 3\r\n                    ? '抵押'\r\n                    : scope.row.carStatus == 4\r\n                      ? '疑似抵押'\r\n                      : scope.row.carStatus == 5\r\n                        ? '疑似黑车'\r\n                        : scope.row.carStatus == 6\r\n                          ? '已入库'\r\n                          : scope.row.carStatus == 7\r\n                            ? '车在法院'\r\n                            : scope.row.carStatus == 8\r\n                              ? '已法拍'\r\n                              : '协商卖车'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"GPS状态\" align=\"center\" prop=\"gpsStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.gpsStatus == 1 ? '部分拆除' : scope.row.gpsStatus == 2 ? '全部拆除' : scope.row.gpsStatus == 3 ? 'GPS正常' :\r\n                '停车30天以上'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车库名称\" align=\"center\" prop=\"garageName\" width=\"130\" />\r\n      <el-table-column label=\"找车团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\" />\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"订单状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ statusList[scope.row.status].label || '暂无数据' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"钥匙邮寄时间\" align=\"center\" prop=\"keyTime\" width=\"180\"></el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button style=\"margin-left: 10px\" size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n            邮寄钥匙\r\n          </el-button>\r\n          <el-button v-if=\"scope.row.status !== 4\" size=\"mini\" type=\"text\"\r\n            @click=\"handleRevoke(scope.row)\">撤销订单</el-button>\r\n          <el-button v-if=\"scope.row.status === 2\" size=\"mini\" type=\"text\"\r\n            @click=\"handleSubmitCost(scope.row)\">提交找车费用</el-button>\r\n          <!-- v-hasPermi=\"['vm_car_order:vm_car_order:remove']\" -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!-- 邮寄钥匙对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"邮寄地址\">\r\n          <el-select @change=\"provinceChange\" v-model=\"form.keyProvince\" value-key=\"children\" placeholder=\"请选择省\">\r\n            <el-option v-for=\"dict in provinceList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict\" />\r\n          </el-select>\r\n          <el-select @change=\"cityChange\" v-model=\"form.keyCity\" value-key=\"children\" placeholder=\"请选择市\"\r\n            style=\"margin-top: 10px\">\r\n            <el-option v-for=\"dict in cityList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict\" />\r\n          </el-select>\r\n          <el-select @change=\"districtChange\" v-model=\"form.keyBorough\" value-key=\"children\" placeholder=\"请选择区\"\r\n            style=\"margin-top: 10px\">\r\n            <el-option v-for=\"dict in districtList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"详细地址\">\r\n          <el-input v-model=\"form.keyAddress\" placeholder=\"请填写详细地址\" clearable />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 提交找车费用对话框 -->\r\n    <el-dialog title=\"提交找车费用\" :visible.sync=\"costDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"costForm\" :model=\"costForm\" :rules=\"costRules\" label-width=\"120px\">\r\n        <el-form-item label=\"佣金\" prop=\"transportationFee\">\r\n          <el-input-number\r\n            v-model=\"costForm.transportationFee\"\r\n            :precision=\"2\"\r\n            :min=\"0\"\r\n            :max=\"999999\"\r\n            placeholder=\"请输入佣金\"\r\n            style=\"width: 100%\">\r\n          </el-input-number>\r\n        </el-form-item>\r\n        <el-form-item label=\"拖车费\" prop=\"towingFee\">\r\n          <el-input-number\r\n            v-model=\"costForm.towingFee\"\r\n            :precision=\"2\"\r\n            :min=\"0\"\r\n            :max=\"999999\"\r\n            placeholder=\"请输入拖车费\"\r\n            style=\"width: 100%\">\r\n          </el-input-number>\r\n        </el-form-item>\r\n        <el-form-item label=\"贴机费\" prop=\"trackerInstallationFee\">\r\n          <el-input-number\r\n            v-model=\"costForm.trackerInstallationFee\"\r\n            :precision=\"2\"\r\n            :min=\"0\"\r\n            :max=\"999999\"\r\n            placeholder=\"请输入贴机费\"\r\n            style=\"width: 100%\">\r\n          </el-input-number>\r\n        </el-form-item>\r\n        <el-form-item label=\"其他报销\" prop=\"otherReimbursement\">\r\n          <el-input-number\r\n            v-model=\"costForm.otherReimbursement\"\r\n            :precision=\"2\"\r\n            :min=\"0\"\r\n            :max=\"999999\"\r\n            placeholder=\"请输入其他报销费用\"\r\n            style=\"width: 100%\">\r\n          </el-input-number>\r\n        </el-form-item>\r\n        <el-form-item label=\"总费用\">\r\n          <el-input :value=\"totalCost\" readonly style=\"width: 100%\">\r\n            <template slot=\"append\">元</template>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitCostForm\">确 定</el-button>\r\n        <el-button @click=\"cancelCost\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listVm_car_order,\r\n  teamVm_car_order,\r\n  getVm_car_order,\r\n  delVm_car_order,\r\n  addVm_car_order,\r\n  updateVm_car_order,\r\n  RevokeVm_car_order,\r\n  submitCarFindCost,\r\n  mailKey,\r\n} from '@/api/vm_car_order/vm_car_order'\r\nimport { listCar_team } from '@/api/car_team/car_team'\r\nimport areaList from '../../../assets/area.json'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nexport default {\r\n  name: 'Vm_car_order',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vm_car_orderList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        customerName: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        garageName: '',\r\n        keyStatus: '',\r\n        status: '',\r\n        teamName: '',\r\n        startTime: '',\r\n        endTime: '',\r\n        originallyTime: '',\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      statusList: [\r\n        { label: '发起订单', value: 0 },\r\n        { label: '已分配', value: 1 },\r\n        { label: '已完成', value: 2 },\r\n        { label: '未完成', value: 3 },\r\n        { label: '已撤销', value: 4 },\r\n      ],\r\n      teamList: [],\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        keyProvince: '',\r\n        keyCity: '',\r\n        keyBorough: '',\r\n        keyAddress: '',\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince: '',\r\n        keyCity: '',\r\n        keyBorough: '',\r\n        keyAddress: '',\r\n      },\r\n      provinceList: areaList,\r\n      cityList: [],\r\n      districtList: [],\r\n      revokeList: {\r\n        id: '',\r\n        status: 4,\r\n      },\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      // 找车费用相关\r\n      costDialogVisible: false,\r\n      costForm: {\r\n        id: '',\r\n        applyNo: '',\r\n        loanId: '',\r\n        transportationFee: 0,\r\n        towingFee: 0,\r\n        trackerInstallationFee: 0,\r\n        otherReimbursement: 0\r\n      },\r\n      costRules: {\r\n        transportationFee: [\r\n          { type: 'number', min: 0, message: '佣金不能小于0', trigger: 'blur' }\r\n        ],\r\n        towingFee: [\r\n          { type: 'number', min: 0, message: '拖车费不能小于0', trigger: 'blur' }\r\n        ],\r\n        trackerInstallationFee: [\r\n          { type: 'number', min: 0, message: '贴机费不能小于0', trigger: 'blur' }\r\n        ],\r\n        otherReimbursement: [\r\n          { type: 'number', min: 0, message: '其他报销不能小于0', trigger: 'blur' }\r\n        ]\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算总费用\r\n    totalCost() {\r\n      const transportationFee = this.costForm.transportationFee || 0\r\n      const towingFee = this.costForm.towingFee || 0\r\n      const trackerInstallationFee = this.costForm.trackerInstallationFee || 0\r\n      const otherReimbursement = this.costForm.otherReimbursement || 0\r\n      return (transportationFee + towingFee + trackerInstallationFee + otherReimbursement).toFixed(2)\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeamList()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleChange(value) {\r\n      this.queryParams.jgName = value\r\n    },\r\n    provinceChange(item) {\r\n      this.form.keyProvince = item.name\r\n      this.cityList = item.children\r\n    },\r\n    cityChange(item) {\r\n      this.form.keyCity = item.name\r\n      this.districtList = item.children\r\n    },\r\n    districtChange(item) {\r\n      this.form.keyBorough = item.name\r\n    },\r\n    async getTeamList() {\r\n      try {\r\n        const res = await listCar_team()\r\n        this.teamList = (res.rows || []).map(item => ({\r\n          label: item.teamName,\r\n          value: item.teamName\r\n        }))\r\n      } catch (e) {\r\n        this.teamList = []\r\n      }\r\n    },\r\n    /** 查询VIEW列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVm_car_order(this.queryParams).then(response => {\r\n        this.vm_car_orderList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        keyProvince: '',\r\n        keyCity: '',\r\n        keyBorough: '',\r\n        keyAddress: '',\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      // delete this.queryParams.originallyTime\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.garageName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.status = null\r\n      this.queryParams.collectionMethod = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.handleQuery()\r\n    },\r\n    restSearch() {\r\n      this.queryParams = {\r\n        customerName: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        garageId: '',\r\n        keyStatus: '',\r\n        status: '',\r\n        teamName: '',\r\n        startTime: '',\r\n        endTime: '',\r\n        originallyTime: '',\r\n        pageNum: 1,\r\n      }\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加VIEW'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.form.id = row.id\r\n      this.form.keyProvince = row.keyProvince\r\n      this.form.keyCity = row.keyCity\r\n      this.form.keyBorough = row.keyBorough\r\n      this.form.keyAddress = row.keyAddress\r\n      const id = row.id || this.ids\r\n      getVm_car_order(id).then(response => {\r\n        // this.form = response.data\r\n        this.open = true\r\n        this.title = '邮寄钥匙'\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 邮寄钥匙逻辑\r\n      mailKey(this.form).then(() => {\r\n        this.$modal.msgSuccess('钥匙邮寄成功')\r\n        this.open = false\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError('邮寄失败：' + (error.message || '未知错误'))\r\n      })\r\n      // this.$refs[\"form\"].validate(valid => {\r\n      //   if (valid) {\r\n      //     if (this.form.id != null) {\r\n      //       updateVm_car_order(this.form).then(response => {\r\n      //         this.$modal.msgSuccess(\"修改成功\")\r\n      //         this.open = false\r\n      //         this.getList()\r\n      //       })\r\n      //     } else {\r\n      //       addVm_car_order(this.form).then(response => {\r\n      //         this.$modal.msgSuccess(\"新增成功\")\r\n      //         this.open = false\r\n      //         this.getList()\r\n      //       })\r\n      //     }\r\n      //   }\r\n      // })\r\n    },\r\n    handleRevoke(row) {\r\n      console.log('1111')\r\n      this.revokeList.id = row.id\r\n      var data = {\r\n        id: row.id,\r\n        status: 4,\r\n      }\r\n      // const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认撤销？')\r\n        .then(() => {\r\n          return RevokeVm_car_order(data)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('撤销成功')\r\n        })\r\n        .catch(err => {\r\n          console.log(err)\r\n        })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认撤销编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVm_car_order(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('撤销成功')\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vm_car_order/vm_car_order/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vm_car_order_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n    // 处理提交找车费用\r\n    handleSubmitCost(row) {\r\n      this.resetCostForm()\r\n      this.costForm.id = row.id\r\n      this.costForm.applyNo = row.applyNo\r\n      this.costForm.loanId = row.loanId\r\n      this.costDialogVisible = true\r\n    },\r\n    // 重置找车费用表单\r\n    resetCostForm() {\r\n      this.costForm = {\r\n        id: '',\r\n        applyNo: '',\r\n        loanId: '',\r\n        transportationFee: 0,\r\n        towingFee: 0,\r\n        trackerInstallationFee: 0,\r\n        otherReimbursement: 0\r\n      }\r\n      if (this.$refs.costForm) {\r\n        this.$refs.costForm.resetFields()\r\n      }\r\n    },\r\n    // 取消找车费用提交\r\n    cancelCost() {\r\n      this.costDialogVisible = false\r\n      this.resetCostForm()\r\n    },\r\n    // 提交找车费用表单\r\n    submitCostForm() {\r\n      this.$refs.costForm.validate(valid => {\r\n        if (valid) {\r\n          // 调用API提交找车费用\r\n          submitCarFindCost(this.costForm).then(() => {\r\n            this.$modal.msgSuccess('找车费用提交成功')\r\n            this.costDialogVisible = false\r\n            this.resetCostForm()\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n  },\r\n}\r\n</script>\r\n"]}]}