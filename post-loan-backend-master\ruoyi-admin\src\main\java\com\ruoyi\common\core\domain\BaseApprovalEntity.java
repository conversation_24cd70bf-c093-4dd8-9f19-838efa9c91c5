package com.ruoyi.common.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

/**
 * 审批流程基础实体类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public abstract class BaseApprovalEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 审批状态常量 - 统一定义
    public static final Integer STATUS_PENDING = 0;           // 通过
    public static final Integer STATUS_APPROVED = 1;          // 全部同意
    public static final Integer STATUS_REJECTED = 2;          // 已拒绝
    public static final Integer STATUS_LEGAL_SUPERVISOR = 3;  // 法诉主管审批
    public static final Integer STATUS_DIRECTOR = 4;          // 总监审批
    public static final Integer STATUS_FINANCE_CC = 5;        // 财务主管/总监抄送
    public static final Integer STATUS_GENERAL_MANAGER = 6;   // 总经理/董事长审批(抄送)

    /** 审批状态 */
    @Excel(name = "审批状态")
    protected Integer approvalStatus;

    /** 拒绝理由 */
    @Excel(name = "拒绝理由")
    protected String rejectReason;

    /** 审批人姓名 */
    @Excel(name = "审批人姓名")
    protected String approveBy;

    /** 审批人角色 */
    @Excel(name = "审批人角色")
    protected String approveRole;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    protected Date applicationTime;

    /** 申请人 */
    @Excel(name = "申请人")
    protected String applicationBy;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    protected Date approveTime;

    /** 审批历史记录 */
    protected String approvalHistory;

    /** 当前审批人 */
    protected String currentApprover;

    /**
     * 获取下一个审批状态
     * 按照 3→4→5→6→1 的顺序进行
     */
    public Integer getNextApprovalStatus() {
        if (this.approvalStatus == null || this.approvalStatus.equals(STATUS_PENDING)) {
            return STATUS_LEGAL_SUPERVISOR;
        } else if (this.approvalStatus.equals(STATUS_LEGAL_SUPERVISOR)) {
            return STATUS_DIRECTOR;
        } else if (this.approvalStatus.equals(STATUS_DIRECTOR)) {
            return STATUS_FINANCE_CC;
        } else if (this.approvalStatus.equals(STATUS_FINANCE_CC)) {
            return STATUS_GENERAL_MANAGER;
        } else if (this.approvalStatus.equals(STATUS_GENERAL_MANAGER)) {
            return STATUS_APPROVED;
        }
        return this.approvalStatus;
    }

    /**
     * 检查用户是否可以审批当前状态
     */
    public boolean canApprove(String userRole) {
        if (this.approvalStatus == null || this.approvalStatus.equals(STATUS_PENDING)) {
            return "法诉主管".equals(userRole);
        } else if (this.approvalStatus.equals(STATUS_LEGAL_SUPERVISOR)) {
            return "总监".equals(userRole);
        } else if (this.approvalStatus.equals(STATUS_DIRECTOR)) {
            return "财务主管".equals(userRole) || "财务总监".equals(userRole);
        } else if (this.approvalStatus.equals(STATUS_FINANCE_CC)) {
            return "总经理".equals(userRole) || "董事长".equals(userRole);
        }
        return false;
    }

    /**
     * 获取当前审批阶段描述
     */
    public String getStatusDescription() {
        if (this.approvalStatus == null || this.approvalStatus.equals(STATUS_PENDING)) {
            return "通过";
        } else if (this.approvalStatus.equals(STATUS_APPROVED)) {
            return "全部同意";
        } else if (this.approvalStatus.equals(STATUS_REJECTED)) {
            return "已拒绝";
        } else if (this.approvalStatus.equals(STATUS_LEGAL_SUPERVISOR)) {
            return "法诉主管审批";
        } else if (this.approvalStatus.equals(STATUS_DIRECTOR)) {
            return "总监审批";
        } else if (this.approvalStatus.equals(STATUS_FINANCE_CC)) {
            return "财务主管/总监抄送";
        } else if (this.approvalStatus.equals(STATUS_GENERAL_MANAGER)) {
            return "总经理/董事长审批(抄送)";
        }
        return "未知状态";
    }

    /**
     * 检查是否为最终状态
     */
    public boolean isFinalStatus() {
        return STATUS_APPROVED.equals(this.approvalStatus) || STATUS_REJECTED.equals(this.approvalStatus);
    }

    // Getter and Setter methods
    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getApproveBy() {
        return approveBy;
    }

    public void setApproveBy(String approveBy) {
        this.approveBy = approveBy;
    }

    public String getApproveRole() {
        return approveRole;
    }

    public void setApproveRole(String approveRole) {
        this.approveRole = approveRole;
    }

    public Date getApplicationTime() {
        return applicationTime;
    }

    public void setApplicationTime(Date applicationTime) {
        this.applicationTime = applicationTime;
    }

    public String getApplicationBy() {
        return applicationBy;
    }

    public void setApplicationBy(String applicationBy) {
        this.applicationBy = applicationBy;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getApprovalHistory() {
        return approvalHistory;
    }

    public void setApprovalHistory(String approvalHistory) {
        this.approvalHistory = approvalHistory;
    }

    public String getCurrentApprover() {
        return currentApprover;
    }

    public void setCurrentApprover(String currentApprover) {
        this.currentApprover = currentApprover;
    }
}
