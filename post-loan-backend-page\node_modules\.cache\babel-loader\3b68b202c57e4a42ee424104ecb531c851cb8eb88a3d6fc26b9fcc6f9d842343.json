{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.APPROVAL_STEPS = exports.APPROVAL_STATUS_MAP = exports.APPROVAL_STATUS_COLOR = exports.APPROVAL_STATUS = exports.APPROVAL_ACTION_MAP = exports.APPROVAL_ACTION = void 0;\nexports.canApprove = canApprove;\nexports.getApprovalStatusColor = getApprovalStatusColor;\nexports.getApprovalStatusText = getApprovalStatusText;\nexports.getCurrentStepIndex = getCurrentStepIndex;\nexports.getNextApprovalStatus = getNextApprovalStatus;\nexports.isFinalStatus = isFinalStatus;\nrequire(\"core-js/modules/es.array.find-index.js\");\nvar _defineProperty2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/defineProperty.js\"));\n/**\n * 统一审批流程常量定义\n * <AUTHOR>\n * @date 2025-08-01\n */\n\n// 审批状态常量\nvar APPROVAL_STATUS = exports.APPROVAL_STATUS = {\n  PENDING: 0,\n  // 通过\n  APPROVED: 1,\n  // 全部同意\n  REJECTED: 2,\n  // 已拒绝\n  LEGAL_SUPERVISOR: 3,\n  // 法诉主管审批\n  DIRECTOR: 4,\n  // 总监审批\n  FINANCE_CC: 5,\n  // 财务主管/总监抄送\n  GENERAL_MANAGER: 6 // 总经理/董事长审批(抄送)\n};\n\n// 审批状态描述映射\nvar APPROVAL_STATUS_MAP = exports.APPROVAL_STATUS_MAP = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, APPROVAL_STATUS.PENDING, '通过'), APPROVAL_STATUS.APPROVED, '全部同意'), APPROVAL_STATUS.REJECTED, '已拒绝'), APPROVAL_STATUS.LEGAL_SUPERVISOR, '法诉主管审批'), APPROVAL_STATUS.DIRECTOR, '总监审批'), APPROVAL_STATUS.FINANCE_CC, '财务主管/总监抄送'), APPROVAL_STATUS.GENERAL_MANAGER, '总经理/董事长审批(抄送)');\n\n// 审批动作常量\nvar APPROVAL_ACTION = exports.APPROVAL_ACTION = {\n  APPROVE: 'approve',\n  // 通过\n  REJECT: 'reject' // 拒绝\n};\n\n// 审批动作描述映射\nvar APPROVAL_ACTION_MAP = exports.APPROVAL_ACTION_MAP = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, APPROVAL_ACTION.APPROVE, '通过'), APPROVAL_ACTION.REJECT, '拒绝');\n\n// 审批状态颜色映射（用于前端显示）\nvar APPROVAL_STATUS_COLOR = exports.APPROVAL_STATUS_COLOR = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, APPROVAL_STATUS.PENDING, 'info'), APPROVAL_STATUS.APPROVED, 'success'), APPROVAL_STATUS.REJECTED, 'danger'), APPROVAL_STATUS.LEGAL_SUPERVISOR, 'warning'), APPROVAL_STATUS.DIRECTOR, 'warning'), APPROVAL_STATUS.FINANCE_CC, 'warning'), APPROVAL_STATUS.GENERAL_MANAGER, 'warning');\n\n// 获取审批状态描述\nfunction getApprovalStatusText(status) {\n  return APPROVAL_STATUS_MAP[status] || '未知状态';\n}\n\n// 获取审批状态颜色\nfunction getApprovalStatusColor(status) {\n  return APPROVAL_STATUS_COLOR[status] || 'info';\n}\n\n// 检查是否为最终状态\nfunction isFinalStatus(status) {\n  return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED;\n}\n\n// 获取下一个审批状态\nfunction getNextApprovalStatus(currentStatus) {\n  switch (currentStatus) {\n    case APPROVAL_STATUS.PENDING:\n      return APPROVAL_STATUS.LEGAL_SUPERVISOR;\n    case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n      return APPROVAL_STATUS.DIRECTOR;\n    case APPROVAL_STATUS.DIRECTOR:\n      return APPROVAL_STATUS.FINANCE_CC;\n    case APPROVAL_STATUS.FINANCE_CC:\n      return APPROVAL_STATUS.GENERAL_MANAGER;\n    case APPROVAL_STATUS.GENERAL_MANAGER:\n      return APPROVAL_STATUS.APPROVED;\n    default:\n      return currentStatus;\n  }\n}\n\n// 检查用户是否可以审批当前状态\nfunction canApprove(currentStatus, userRole) {\n  switch (currentStatus) {\n    case APPROVAL_STATUS.PENDING:\n      return userRole === '法诉主管';\n    case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n      return userRole === '总监';\n    case APPROVAL_STATUS.DIRECTOR:\n      return userRole === '财务主管' || userRole === '财务总监';\n    case APPROVAL_STATUS.FINANCE_CC:\n      return userRole === '总经理' || userRole === '董事长';\n    default:\n      return false;\n  }\n}\n\n// 审批流程步骤定义（用于进度条显示）\nvar APPROVAL_STEPS = exports.APPROVAL_STEPS = [{\n  title: '法诉主管审批',\n  status: APPROVAL_STATUS.LEGAL_SUPERVISOR,\n  description: '法诉主管进行初步审批'\n}, {\n  title: '总监审批',\n  status: APPROVAL_STATUS.DIRECTOR,\n  description: '总监进行二级审批'\n}, {\n  title: '财务主管/总监抄送',\n  status: APPROVAL_STATUS.FINANCE_CC,\n  description: '财务部门确认'\n}, {\n  title: '总经理/董事长审批',\n  status: APPROVAL_STATUS.GENERAL_MANAGER,\n  description: '最高级别审批'\n}, {\n  title: '审批完成',\n  status: APPROVAL_STATUS.APPROVED,\n  description: '所有审批流程完成'\n}];\n\n// 获取当前审批步骤索引\nfunction getCurrentStepIndex(status) {\n  return APPROVAL_STEPS.findIndex(function (step) {\n    return step.status === status;\n  });\n}", "map": {"version": 3, "names": ["APPROVAL_STATUS", "exports", "PENDING", "APPROVED", "REJECTED", "LEGAL_SUPERVISOR", "DIRECTOR", "FINANCE_CC", "GENERAL_MANAGER", "APPROVAL_STATUS_MAP", "_defineProperty2", "default", "APPROVAL_ACTION", "APPROVE", "REJECT", "APPROVAL_ACTION_MAP", "APPROVAL_STATUS_COLOR", "getApprovalStatusText", "status", "getApprovalStatusColor", "isFinalStatus", "getNextApprovalStatus", "currentStatus", "canApprove", "userRole", "APPROVAL_STEPS", "title", "description", "getCurrentStepIndex", "findIndex", "step"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/utils/approvalConstants.js"], "sourcesContent": ["/**\n * 统一审批流程常量定义\n * <AUTHOR>\n * @date 2025-08-01\n */\n\n// 审批状态常量\nexport const APPROVAL_STATUS = {\n  PENDING: 0,           // 通过\n  APPROVED: 1,          // 全部同意\n  REJECTED: 2,          // 已拒绝\n  LEGAL_SUPERVISOR: 3,  // 法诉主管审批\n  DIRECTOR: 4,          // 总监审批\n  FINANCE_CC: 5,        // 财务主管/总监抄送\n  GENERAL_MANAGER: 6    // 总经理/董事长审批(抄送)\n}\n\n// 审批状态描述映射\nexport const APPROVAL_STATUS_MAP = {\n  [APPROVAL_STATUS.PENDING]: '通过',\n  [APPROVAL_STATUS.APPROVED]: '全部同意',\n  [APPROVAL_STATUS.REJECTED]: '已拒绝',\n  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: '法诉主管审批',\n  [APPROVAL_STATUS.DIRECTOR]: '总监审批',\n  [APPROVAL_STATUS.FINANCE_CC]: '财务主管/总监抄送',\n  [APPROVAL_STATUS.GENERAL_MANAGER]: '总经理/董事长审批(抄送)'\n}\n\n// 审批动作常量\nexport const APPROVAL_ACTION = {\n  APPROVE: 'approve',   // 通过\n  REJECT: 'reject'      // 拒绝\n}\n\n// 审批动作描述映射\nexport const APPROVAL_ACTION_MAP = {\n  [APPROVAL_ACTION.APPROVE]: '通过',\n  [APPROVAL_ACTION.REJECT]: '拒绝'\n}\n\n// 审批状态颜色映射（用于前端显示）\nexport const APPROVAL_STATUS_COLOR = {\n  [APPROVAL_STATUS.PENDING]: 'info',\n  [APPROVAL_STATUS.APPROVED]: 'success',\n  [APPROVAL_STATUS.REJECTED]: 'danger',\n  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: 'warning',\n  [APPROVAL_STATUS.DIRECTOR]: 'warning',\n  [APPROVAL_STATUS.FINANCE_CC]: 'warning',\n  [APPROVAL_STATUS.GENERAL_MANAGER]: 'warning'\n}\n\n// 获取审批状态描述\nexport function getApprovalStatusText(status) {\n  return APPROVAL_STATUS_MAP[status] || '未知状态'\n}\n\n// 获取审批状态颜色\nexport function getApprovalStatusColor(status) {\n  return APPROVAL_STATUS_COLOR[status] || 'info'\n}\n\n// 检查是否为最终状态\nexport function isFinalStatus(status) {\n  return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED\n}\n\n// 获取下一个审批状态\nexport function getNextApprovalStatus(currentStatus) {\n  switch (currentStatus) {\n    case APPROVAL_STATUS.PENDING:\n      return APPROVAL_STATUS.LEGAL_SUPERVISOR\n    case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n      return APPROVAL_STATUS.DIRECTOR\n    case APPROVAL_STATUS.DIRECTOR:\n      return APPROVAL_STATUS.FINANCE_CC\n    case APPROVAL_STATUS.FINANCE_CC:\n      return APPROVAL_STATUS.GENERAL_MANAGER\n    case APPROVAL_STATUS.GENERAL_MANAGER:\n      return APPROVAL_STATUS.APPROVED\n    default:\n      return currentStatus\n  }\n}\n\n// 检查用户是否可以审批当前状态\nexport function canApprove(currentStatus, userRole) {\n  switch (currentStatus) {\n    case APPROVAL_STATUS.PENDING:\n      return userRole === '法诉主管'\n    case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n      return userRole === '总监'\n    case APPROVAL_STATUS.DIRECTOR:\n      return userRole === '财务主管' || userRole === '财务总监'\n    case APPROVAL_STATUS.FINANCE_CC:\n      return userRole === '总经理' || userRole === '董事长'\n    default:\n      return false\n  }\n}\n\n// 审批流程步骤定义（用于进度条显示）\nexport const APPROVAL_STEPS = [\n  {\n    title: '法诉主管审批',\n    status: APPROVAL_STATUS.LEGAL_SUPERVISOR,\n    description: '法诉主管进行初步审批'\n  },\n  {\n    title: '总监审批',\n    status: APPROVAL_STATUS.DIRECTOR,\n    description: '总监进行二级审批'\n  },\n  {\n    title: '财务主管/总监抄送',\n    status: APPROVAL_STATUS.FINANCE_CC,\n    description: '财务部门确认'\n  },\n  {\n    title: '总经理/董事长审批',\n    status: APPROVAL_STATUS.GENERAL_MANAGER,\n    description: '最高级别审批'\n  },\n  {\n    title: '审批完成',\n    status: APPROVAL_STATUS.APPROVED,\n    description: '所有审批流程完成'\n  }\n]\n\n// 获取当前审批步骤索引\nexport function getCurrentStepIndex(status) {\n  return APPROVAL_STEPS.findIndex(step => step.status === status)\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG;EAC7BE,OAAO,EAAE,CAAC;EAAY;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,gBAAgB,EAAE,CAAC;EAAG;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,UAAU,EAAE,CAAC;EAAS;EACtBC,eAAe,EAAE,CAAC,CAAI;AACxB,CAAC;;AAED;AACO,IAAMC,mBAAmB,GAAAR,OAAA,CAAAQ,mBAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAC7BX,eAAe,CAACE,OAAO,EAAG,IAAI,GAC9BF,eAAe,CAACG,QAAQ,EAAG,MAAM,GACjCH,eAAe,CAACI,QAAQ,EAAG,KAAK,GAChCJ,eAAe,CAACK,gBAAgB,EAAG,QAAQ,GAC3CL,eAAe,CAACM,QAAQ,EAAG,MAAM,GACjCN,eAAe,CAACO,UAAU,EAAG,WAAW,GACxCP,eAAe,CAACQ,eAAe,EAAG,eAAe,CACnD;;AAED;AACO,IAAMI,eAAe,GAAAX,OAAA,CAAAW,eAAA,GAAG;EAC7BC,OAAO,EAAE,SAAS;EAAI;EACtBC,MAAM,EAAE,QAAQ,CAAM;AACxB,CAAC;;AAED;AACO,IAAMC,mBAAmB,GAAAd,OAAA,CAAAc,mBAAA,OAAAL,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAC7BC,eAAe,CAACC,OAAO,EAAG,IAAI,GAC9BD,eAAe,CAACE,MAAM,EAAG,IAAI,CAC/B;;AAED;AACO,IAAME,qBAAqB,GAAAf,OAAA,CAAAe,qBAAA,OAAAN,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAC/BX,eAAe,CAACE,OAAO,EAAG,MAAM,GAChCF,eAAe,CAACG,QAAQ,EAAG,SAAS,GACpCH,eAAe,CAACI,QAAQ,EAAG,QAAQ,GACnCJ,eAAe,CAACK,gBAAgB,EAAG,SAAS,GAC5CL,eAAe,CAACM,QAAQ,EAAG,SAAS,GACpCN,eAAe,CAACO,UAAU,EAAG,SAAS,GACtCP,eAAe,CAACQ,eAAe,EAAG,SAAS,CAC7C;;AAED;AACO,SAASS,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAOT,mBAAmB,CAACS,MAAM,CAAC,IAAI,MAAM;AAC9C;;AAEA;AACO,SAASC,sBAAsBA,CAACD,MAAM,EAAE;EAC7C,OAAOF,qBAAqB,CAACE,MAAM,CAAC,IAAI,MAAM;AAChD;;AAEA;AACO,SAASE,aAAaA,CAACF,MAAM,EAAE;EACpC,OAAOA,MAAM,KAAKlB,eAAe,CAACG,QAAQ,IAAIe,MAAM,KAAKlB,eAAe,CAACI,QAAQ;AACnF;;AAEA;AACO,SAASiB,qBAAqBA,CAACC,aAAa,EAAE;EACnD,QAAQA,aAAa;IACnB,KAAKtB,eAAe,CAACE,OAAO;MAC1B,OAAOF,eAAe,CAACK,gBAAgB;IACzC,KAAKL,eAAe,CAACK,gBAAgB;MACnC,OAAOL,eAAe,CAACM,QAAQ;IACjC,KAAKN,eAAe,CAACM,QAAQ;MAC3B,OAAON,eAAe,CAACO,UAAU;IACnC,KAAKP,eAAe,CAACO,UAAU;MAC7B,OAAOP,eAAe,CAACQ,eAAe;IACxC,KAAKR,eAAe,CAACQ,eAAe;MAClC,OAAOR,eAAe,CAACG,QAAQ;IACjC;MACE,OAAOmB,aAAa;EACxB;AACF;;AAEA;AACO,SAASC,UAAUA,CAACD,aAAa,EAAEE,QAAQ,EAAE;EAClD,QAAQF,aAAa;IACnB,KAAKtB,eAAe,CAACE,OAAO;MAC1B,OAAOsB,QAAQ,KAAK,MAAM;IAC5B,KAAKxB,eAAe,CAACK,gBAAgB;MACnC,OAAOmB,QAAQ,KAAK,IAAI;IAC1B,KAAKxB,eAAe,CAACM,QAAQ;MAC3B,OAAOkB,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM;IACnD,KAAKxB,eAAe,CAACO,UAAU;MAC7B,OAAOiB,QAAQ,KAAK,KAAK,IAAIA,QAAQ,KAAK,KAAK;IACjD;MACE,OAAO,KAAK;EAChB;AACF;;AAEA;AACO,IAAMC,cAAc,GAAAxB,OAAA,CAAAwB,cAAA,GAAG,CAC5B;EACEC,KAAK,EAAE,QAAQ;EACfR,MAAM,EAAElB,eAAe,CAACK,gBAAgB;EACxCsB,WAAW,EAAE;AACf,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbR,MAAM,EAAElB,eAAe,CAACM,QAAQ;EAChCqB,WAAW,EAAE;AACf,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBR,MAAM,EAAElB,eAAe,CAACO,UAAU;EAClCoB,WAAW,EAAE;AACf,CAAC,EACD;EACED,KAAK,EAAE,WAAW;EAClBR,MAAM,EAAElB,eAAe,CAACQ,eAAe;EACvCmB,WAAW,EAAE;AACf,CAAC,EACD;EACED,KAAK,EAAE,MAAM;EACbR,MAAM,EAAElB,eAAe,CAACG,QAAQ;EAChCwB,WAAW,EAAE;AACf,CAAC,CACF;;AAED;AACO,SAASC,mBAAmBA,CAACV,MAAM,EAAE;EAC1C,OAAOO,cAAc,CAACI,SAAS,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAACZ,MAAM,KAAKA,MAAM;EAAA,EAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}