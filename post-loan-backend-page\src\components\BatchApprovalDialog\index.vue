<template>
  <el-dialog
    title="批量审批"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="batch-approval-dialog">
      <!-- 选中记录信息 -->
      <div class="selected-info">
        <el-alert
          :title="`已选择 ${selectedCount} 条记录进行批量审批`"
          type="info"
          :closable="false"
          show-icon
        ></el-alert>
      </div>

      <!-- 批量审批表单 -->
      <el-form ref="batchForm" :model="form" :rules="rules" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="审批结果" prop="approvalAction">
          <el-radio-group v-model="form.approvalAction">
            <el-radio :label="APPROVAL_ACTION.APPROVE">批量通过</el-radio>
            <el-radio :label="APPROVAL_ACTION.REJECT">批量拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          label="拒绝原因" 
          prop="rejectReason" 
          v-if="form.approvalAction === APPROVAL_ACTION.REJECT"
        >
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>

        <el-form-item label="审批备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入审批备注（可选）"
          ></el-input>
        </el-form-item>
      </el-form>

      <!-- 警告提示 -->
      <div class="warning-tip" v-if="form.approvalAction">
        <el-alert
          :title="getWarningText()"
          type="warning"
          :closable="false"
          show-icon
        ></el-alert>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  APPROVAL_ACTION
} from '@/utils/approvalConstants'

export default {
  name: 'BatchApprovalDialog',
  props: {
    // 是否显示对话框
    value: {
      type: Boolean,
      default: false
    },
    // 选中的记录数量
    selectedCount: {
      type: Number,
      default: 0
    },
    // 选中的记录ID列表
    selectedIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      APPROVAL_ACTION,
      loading: false,
      form: {
        approvalAction: '',
        rejectReason: '',
        remark: ''
      },
      rules: {
        approvalAction: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        rejectReason: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    value(newVal) {
      if (newVal) {
        this.resetForm()
      }
    },
    'form.approvalAction'(newVal) {
      if (newVal !== APPROVAL_ACTION.REJECT) {
        this.form.rejectReason = ''
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        approvalAction: '',
        rejectReason: '',
        remark: ''
      }
      this.$nextTick(() => {
        if (this.$refs.batchForm) {
          this.$refs.batchForm.clearValidate()
        }
      })
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.resetForm()
    },

    // 获取警告提示文本
    getWarningText() {
      if (this.form.approvalAction === APPROVAL_ACTION.APPROVE) {
        return `确认要批量通过这 ${this.selectedCount} 条记录吗？此操作不可撤销。`
      } else if (this.form.approvalAction === APPROVAL_ACTION.REJECT) {
        return `确认要批量拒绝这 ${this.selectedCount} 条记录吗？此操作不可撤销。`
      }
      return ''
    },

    // 提交批量审批
    handleSubmit() {
      if (this.selectedIds.length === 0) {
        this.$message.error('请先选择要审批的记录')
        return
      }

      this.$refs.batchForm.validate((valid) => {
        if (valid) {
          this.loading = true
          const batchRequest = {
            ids: this.selectedIds,
            approvalAction: this.form.approvalAction,
            rejectReason: this.form.rejectReason,
            remark: this.form.remark
          }

          this.$emit('batch-approve', batchRequest)
        }
      })
    },

    // 批量审批完成回调
    onBatchApprovalComplete() {
      this.loading = false
      this.handleClose()
    },

    // 批量审批失败回调
    onBatchApprovalError() {
      this.loading = false
    }
  }
}
</script>

<style scoped>
.batch-approval-dialog {
  max-height: 500px;
  overflow-y: auto;
}

.selected-info {
  margin-bottom: 20px;
}

.warning-tip {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
