<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_car_order_examine.mapper.VwCarOrderExamineMapper">
    
    <resultMap type="VwCarOrderExamine" id="VwCarOrderExamineResult">
        <result property="id"    column="id"    />
        <result property="transportationFee"    column="transportation_fee"    />
        <result property="towingFee"    column="towing_fee"    />
        <result property="trackerInstallationFee"    column="tracker_installation_fee"    />
        <result property="otherReimbursement"    column="other_reimbursement"    />
        <result property="totalCost"    column="total_cost"    />
        <!-- 统一审批字段映射 -->
        <result property="approvalStatus"    column="status"    />
        <result property="approveTime"    column="examine_time"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveRole"    column="approve_role"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="applicationBy"    column="application_by"    />
        <result property="approvalHistory"    column="approval_history"    />
        <result property="currentApprover"    column="current_approver"    />
        <!-- 业务字段 -->
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="libraryStatus"    column="library_status"    />
        <result property="inboundTime"    column="inbound_time"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="allocationTime"    column="allocation_time"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="teamName"    column="team_name"    />
        <result property="jgName"    column="jg_name"    />
        <result property="garageName"    column="garage_name"    />
    </resultMap>

    <sql id="selectVwCarOrderExamineVo">
        select id, transportation_fee, towing_fee, tracker_installation_fee, other_reimbursement, total_cost,
               status, examine_time, reject_reason, approve_by, approve_role, application_time, application_by,
               approval_history, current_approver, team_id, garage_id, library_status, inbound_time,
               locating_commission, key_status, collection_method, allocation_time, customer_name, customer_id,
               apply_id, mobile_phone, plate_no, team_name, jg_name, garage_name
        from vw_car_order_examine
    </sql>

    <select id="selectVwCarOrderExamineList" parameterType="VwCarOrderExamine" resultMap="VwCarOrderExamineResult">
        <include refid="selectVwCarOrderExamineVo"/>
        <where>  
            <if test="transportationFee != null "> and transportation_fee = #{transportationFee}</if>
            <if test="towingFee != null "> and towing_fee = #{towingFee}</if>
            <if test="trackerInstallationFee != null "> and tracker_installation_fee = #{trackerInstallationFee}</if>
            <if test="otherReimbursement != null "> and other_reimbursement = #{otherReimbursement}</if>
            <if test="totalCost != null "> and total_cost = #{totalCost}</if>
            <if test="approvalStatus != null "> and status = #{approvalStatus}</if>
            <if test="approveTime != null "> and examine_time = #{approveTime}</if>
            <if test="rejectReason != null  and rejectReason != ''"> and reject_reason = #{rejectReason}</if>
            <if test="approveBy != null  and approveBy != ''"> and approve_by = #{approveBy}</if>
            <if test="approveRole != null  and approveRole != ''"> and approve_role = #{approveRole}</if>
            <if test="applicationBy != null  and applicationBy != ''"> and application_by = #{applicationBy}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="libraryStatus != null "> and library_status = #{libraryStatus}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="locatingCommission != null "> and locating_commission = #{locatingCommission}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="startTime != null and endTime != null"> AND allocation_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="jgName != null  and jgName != ''"> and jg_name like concat('%', #{jgName}, '%')</if>
            <if test="garageName != null  and garageName != ''"> and garage_name like concat('%', #{garageName}, '%')</if>
        </where>
    </select>
    
    <select id="selectVwCarOrderExamineById" parameterType="String" resultMap="VwCarOrderExamineResult">
        <include refid="selectVwCarOrderExamineVo"/>
        where id = #{id}
    </select>

    <insert id="insertVwCarOrderExamine" parameterType="VwCarOrderExamine">
        insert into vw_car_order_examine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="transportationFee != null">transportation_fee,</if>
            <if test="towingFee != null">towing_fee,</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee,</if>
            <if test="otherReimbursement != null">other_reimbursement,</if>
            <if test="totalCost != null">total_cost,</if>
            <if test="approvalStatus != null">status,</if>
            <if test="approveTime != null">examine_time,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveRole != null">approve_role,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="applicationBy != null">application_by,</if>
            <if test="approvalHistory != null">approval_history,</if>
            <if test="currentApprover != null">current_approver,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="libraryStatus != null">library_status,</if>
            <if test="inboundTime != null">inbound_time,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="allocationTime != null">allocation_time,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="teamName != null">team_name,</if>
            <if test="jgName != null">jg_name,</if>
            <if test="garageName != null">garage_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="transportationFee != null">#{transportationFee},</if>
            <if test="towingFee != null">#{towingFee},</if>
            <if test="trackerInstallationFee != null">#{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">#{otherReimbursement},</if>
            <if test="totalCost != null">#{totalCost},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="approveTime != null">#{approveTime},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveRole != null">#{approveRole},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="applicationBy != null">#{applicationBy},</if>
            <if test="approvalHistory != null">#{approvalHistory},</if>
            <if test="currentApprover != null">#{currentApprover},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="libraryStatus != null">#{libraryStatus},</if>
            <if test="inboundTime != null">#{inboundTime},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="allocationTime != null">#{allocationTime},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="jgName != null">#{jgName},</if>
            <if test="garageName != null">#{garageName},</if>
         </trim>
    </insert>

    <update id="updateVwCarOrderExamine" parameterType="VwCarOrderExamine">
        update vw_car_order_examine
        <trim prefix="SET" suffixOverrides=",">
            <if test="transportationFee != null">transportation_fee = #{transportationFee},</if>
            <if test="towingFee != null">towing_fee = #{towingFee},</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee = #{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">other_reimbursement = #{otherReimbursement},</if>
            <if test="totalCost != null">total_cost = #{totalCost},</if>
            <if test="approvalStatus != null">status = #{approvalStatus},</if>
            <if test="approveTime != null">examine_time = #{approveTime},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveRole != null">approve_role = #{approveRole},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="applicationBy != null">application_by = #{applicationBy},</if>
            <if test="approvalHistory != null">approval_history = #{approvalHistory},</if>
            <if test="currentApprover != null">current_approver = #{currentApprover},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="libraryStatus != null">library_status = #{libraryStatus},</if>
            <if test="inboundTime != null">inbound_time = #{inboundTime},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="allocationTime != null">allocation_time = #{allocationTime},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="jgName != null">jg_name = #{jgName},</if>
            <if test="garageName != null">garage_name = #{garageName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwCarOrderExamineById" parameterType="String">
        delete from vw_car_order_examine where id = #{id}
    </delete>

    <delete id="deleteVwCarOrderExamineByIds" parameterType="String">
        delete from vw_car_order_examine where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>