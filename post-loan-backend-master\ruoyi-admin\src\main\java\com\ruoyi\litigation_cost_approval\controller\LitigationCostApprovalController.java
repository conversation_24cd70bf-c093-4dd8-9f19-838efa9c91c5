package com.ruoyi.litigation_cost_approval.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseApprovalController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_cost_approval.domain.LitigationCostApproval;
import com.ruoyi.litigation_cost_approval.service.ILitigationCostApprovalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 法诉费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/litigation_cost_approval/litigation_cost_approval")
public class LitigationCostApprovalController extends BaseApprovalController<LitigationCostApproval, ILitigationCostApprovalService>
{
    @Autowired
    private ILitigationCostApprovalService litigationCostApprovalService;

    @Override
    protected ILitigationCostApprovalService getService() {
        return litigationCostApprovalService;
    }

    /**
     * 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCostApproval litigationCostApproval)
    {
        startPage();
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        return getDataTable(list);
    }

    /**
     * 导出法诉费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:export')")
    @Log(title = "法诉费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCostApproval litigationCostApproval)
    {
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        ExcelUtil<LitigationCostApproval> util = new ExcelUtil<LitigationCostApproval>(LitigationCostApproval.class);
        util.exportExcel(response, list, "法诉费用审批数据");
    }

    /**
     * 获取法诉费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationCostApprovalService.selectLitigationCostApprovalById(id));
    }

    /**
     * 根据法诉案件ID获取费用提交记录详情（用于审批弹窗）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/records/{litigationCaseId}")
    public AjaxResult getSubmissionRecords(@PathVariable("litigationCaseId") Long litigationCaseId)
    {
        List<LitigationCostApproval> records = litigationCostApprovalService.selectSubmissionRecordsByLitigationCaseId(litigationCaseId);
        return success(records);
    }

    /**
     * 新增法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:add')")
    @Log(title = "法诉费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.insertLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 修改法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:edit')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 删除法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:remove')")
    @Log(title = "法诉费用审批", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationCostApprovalService.deleteLitigationCostApprovalByIds(ids));
    }

    /**
     * 单个审批费用记录 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approveRecord(@RequestBody ApprovalRequest request)
    {
        return super.approve(request);
    }

    /**
     * 批量审批费用记录 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApproveRecords(@RequestBody BatchApprovalRequest request)
    {
        return super.batchApprove(request);
    }

    /**
     * 获取待审批列表 - 使用统一审批接口
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/pending")
    public AjaxResult getPendingList()
    {
        return super.getPendingApprovalList();
    }

    /**
     * 获取审批状态统计
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<Map<String, Object>> statistics = litigationCostApprovalService.getApprovalStatistics();
        return success(statistics);
    }

    /**
     * 查询待审批记录数量
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingCount()
    {
        int count = litigationCostApprovalService.countPendingApproval();
        return success(count);
    }
}
