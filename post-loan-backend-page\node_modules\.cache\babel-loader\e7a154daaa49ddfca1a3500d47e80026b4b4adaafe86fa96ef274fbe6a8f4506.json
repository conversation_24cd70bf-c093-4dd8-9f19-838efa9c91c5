{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _approvalConstants = require(\"@/utils/approvalConstants\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'ApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 对话框标题\n    title: {\n      type: String,\n      default: '审批'\n    },\n    // 审批数据\n    approvalData: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    // 是否显示进度条\n    showProgress: {\n      type: Boolean,\n      default: true\n    },\n    // 用户角色\n    userRole: {\n      type: String,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      APPROVAL_STATUS: _approvalConstants.APPROVAL_STATUS,\n      APPROVAL_ACTION: _approvalConstants.APPROVAL_ACTION,\n      APPROVAL_STEPS: _approvalConstants.APPROVAL_STEPS,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [{\n          required: true,\n          message: '请选择审批结果',\n          trigger: 'change'\n        }],\n        rejectReason: [{\n          required: true,\n          message: '请输入拒绝原因',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: {\n    visible: {\n      get: function get() {\n        return this.value;\n      },\n      set: function set(val) {\n        this.$emit('input', val);\n      }\n    }\n  },\n  watch: {\n    value: function value(newVal) {\n      if (newVal) {\n        this.resetForm();\n      }\n    },\n    'form.approvalAction': function formApprovalAction(newVal) {\n      if (newVal !== _approvalConstants.APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = '';\n      }\n    }\n  },\n  methods: {\n    getApprovalStatusText: _approvalConstants.getApprovalStatusText,\n    getApprovalStatusColor: _approvalConstants.getApprovalStatusColor,\n    getCurrentStepIndex: _approvalConstants.getCurrentStepIndex,\n    // 重置表单\n    resetForm: function resetForm() {\n      var _this = this;\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      };\n      this.$nextTick(function () {\n        if (_this.$refs.approvalForm) {\n          _this.$refs.approvalForm.clearValidate();\n        }\n      });\n    },\n    // 关闭对话框\n    handleClose: function handleClose() {\n      this.visible = false;\n      this.resetForm();\n    },\n    // 提交审批\n    handleSubmit: function handleSubmit() {\n      var _this2 = this;\n      this.$refs.approvalForm.validate(function (valid) {\n        if (valid) {\n          // 检查用户权限\n          if (!(0, _approvalConstants.canApprove)(_this2.approvalData.approvalStatus, _this2.userRole)) {\n            _this2.$message.error('您没有权限进行当前阶段的审批');\n            return;\n          }\n          _this2.loading = true;\n          var approvalRequest = {\n            id: _this2.approvalData.id,\n            approvalAction: _this2.form.approvalAction,\n            rejectReason: _this2.form.rejectReason,\n            remark: _this2.form.remark\n          };\n          _this2.$emit('approve', approvalRequest);\n        }\n      });\n    },\n    // 审批完成回调\n    onApprovalComplete: function onApprovalComplete() {\n      this.loading = false;\n      this.handleClose();\n    },\n    // 审批失败回调\n    onApprovalError: function onApprovalError() {\n      this.loading = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["_approvalConstants", "require", "name", "props", "value", "type", "Boolean", "default", "title", "String", "approvalData", "Object", "showProgress", "userRole", "data", "APPROVAL_STATUS", "APPROVAL_ACTION", "APPROVAL_STEPS", "loading", "form", "approvalAction", "rejectReason", "remark", "rules", "required", "message", "trigger", "computed", "visible", "get", "set", "val", "$emit", "watch", "newVal", "resetForm", "formApprovalAction", "REJECT", "methods", "getApprovalStatusText", "getApprovalStatusColor", "getCurrentStepIndex", "_this", "$nextTick", "$refs", "approvalForm", "clearValidate", "handleClose", "handleSubmit", "_this2", "validate", "valid", "canApprove", "approvalStatus", "$message", "error", "approvalRequest", "id", "onApprovalComplete", "onApprovalError"], "sources": ["src/components/ApprovalDialog/index.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"approval-dialog\">\n      <!-- 审批信息展示 -->\n      <div class=\"approval-info\" v-if=\"approvalData\">\n        <el-descriptions title=\"审批信息\" :column=\"2\" border>\n          <el-descriptions-item label=\"当前状态\">\n            <el-tag :type=\"getApprovalStatusColor(approvalData.approvalStatus)\">\n              {{ getApprovalStatusText(approvalData.approvalStatus) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请人\">\n            {{ approvalData.applicationBy || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请时间\">\n            {{ approvalData.applicationTime || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"审批人\">\n            {{ approvalData.approveBy || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <!-- 审批流程进度 -->\n      <div class=\"approval-progress\" v-if=\"showProgress\">\n        <el-steps :active=\"getCurrentStepIndex(approvalData.approvalStatus)\" finish-status=\"success\">\n          <el-step\n            v-for=\"step in APPROVAL_STEPS\"\n            :key=\"step.status\"\n            :title=\"step.title\"\n            :description=\"step.description\"\n          ></el-step>\n        </el-steps>\n      </div>\n\n      <!-- 审批操作表单 -->\n      <el-form ref=\"approvalForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 审批历史 -->\n      <div class=\"approval-history\" v-if=\"approvalData && approvalData.approvalHistory\">\n        <el-divider content-position=\"left\">审批历史</el-divider>\n        <div class=\"history-content\">\n          <pre>{{ approvalData.approvalHistory }}</pre>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_STATUS,\n  APPROVAL_ACTION,\n  APPROVAL_STEPS,\n  getApprovalStatusText,\n  getApprovalStatusColor,\n  getCurrentStepIndex,\n  canApprove\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'ApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 对话框标题\n    title: {\n      type: String,\n      default: '审批'\n    },\n    // 审批数据\n    approvalData: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示进度条\n    showProgress: {\n      type: Boolean,\n      default: true\n    },\n    // 用户角色\n    userRole: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      APPROVAL_STATUS,\n      APPROVAL_ACTION,\n      APPROVAL_STEPS,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    getApprovalStatusText,\n    getApprovalStatusColor,\n    getCurrentStepIndex,\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.approvalForm) {\n          this.$refs.approvalForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 提交审批\n    handleSubmit() {\n      this.$refs.approvalForm.validate((valid) => {\n        if (valid) {\n          // 检查用户权限\n          if (!canApprove(this.approvalData.approvalStatus, this.userRole)) {\n            this.$message.error('您没有权限进行当前阶段的审批')\n            return\n          }\n\n          this.loading = true\n          const approvalRequest = {\n            id: this.approvalData.id,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('approve', approvalRequest)\n        }\n      })\n    },\n\n    // 审批完成回调\n    onApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 审批失败回调\n    onApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-dialog {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.approval-info {\n  margin-bottom: 20px;\n}\n\n.approval-progress {\n  margin: 20px 0;\n}\n\n.approval-history {\n  margin-top: 20px;\n}\n\n.history-content {\n  background-color: #f5f5f5;\n  padding: 10px;\n  border-radius: 4px;\n  max-height: 150px;\n  overflow-y: auto;\n}\n\n.history-content pre {\n  margin: 0;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": ";;;;;;AA0FA,IAAAA,kBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,YAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAK,YAAA;MACAP,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAM,QAAA;MACAR,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA,EAAAA,kCAAA;MACAC,eAAA,EAAAA,kCAAA;MACAC,cAAA,EAAAA,iCAAA;MACAC,OAAA;MACAC,IAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAH,cAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,YAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAzB,KAAA;MACA;MACA0B,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,UAAAD,GAAA;MACA;IACA;EACA;EACAE,KAAA;IACA7B,KAAA,WAAAA,MAAA8B,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,SAAA;MACA;IACA;IACA,gCAAAC,mBAAAF,MAAA;MACA,IAAAA,MAAA,KAAAlB,kCAAA,CAAAqB,MAAA;QACA,KAAAlB,IAAA,CAAAE,YAAA;MACA;IACA;EACA;EACAiB,OAAA;IACAC,qBAAA,EAAAA,wCAAA;IACAC,sBAAA,EAAAA,yCAAA;IACAC,mBAAA,EAAAA,sCAAA;IAEA;IACAN,SAAA,WAAAA,UAAA;MAAA,IAAAO,KAAA;MACA,KAAAvB,IAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA,KAAAqB,SAAA;QACA,IAAAD,KAAA,CAAAE,KAAA,CAAAC,YAAA;UACAH,KAAA,CAAAE,KAAA,CAAAC,YAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAnB,OAAA;MACA,KAAAO,SAAA;IACA;IAEA;IACAa,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAL,KAAA,CAAAC,YAAA,CAAAK,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,SAAAC,6BAAA,EAAAH,MAAA,CAAAvC,YAAA,CAAA2C,cAAA,EAAAJ,MAAA,CAAApC,QAAA;YACAoC,MAAA,CAAAK,QAAA,CAAAC,KAAA;YACA;UACA;UAEAN,MAAA,CAAA/B,OAAA;UACA,IAAAsC,eAAA;YACAC,EAAA,EAAAR,MAAA,CAAAvC,YAAA,CAAA+C,EAAA;YACArC,cAAA,EAAA6B,MAAA,CAAA9B,IAAA,CAAAC,cAAA;YACAC,YAAA,EAAA4B,MAAA,CAAA9B,IAAA,CAAAE,YAAA;YACAC,MAAA,EAAA2B,MAAA,CAAA9B,IAAA,CAAAG;UACA;UAEA2B,MAAA,CAAAjB,KAAA,YAAAwB,eAAA;QACA;MACA;IACA;IAEA;IACAE,kBAAA,WAAAA,mBAAA;MACA,KAAAxC,OAAA;MACA,KAAA6B,WAAA;IACA;IAEA;IACAY,eAAA,WAAAA,gBAAA;MACA,KAAAzC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}