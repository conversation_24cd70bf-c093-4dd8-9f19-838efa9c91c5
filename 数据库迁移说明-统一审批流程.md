# 统一审批流程数据库迁移说明

## 概述

为了实现法诉费用模块和找车模块的统一审批流程，需要对数据库表结构进行调整，确保两个模块使用相同的审批字段和状态定义。

## 涉及的表

### 1. litigation_cost 表（法诉费用表）
- **状态**: 已有基本审批字段，需要验证字段类型和完整性
- **主要字段**: `approval_status`, `reasons`, `approve_by`, `approve_role`, `application_time`, `application_by`

### 2. vw_car_order_examine 表（找车费用审批表）
- **状态**: 缺少部分统一审批字段，需要添加
- **现有字段**: `status`, `examine_time`, `reject_reason`
- **需要添加的字段**: `approve_by`, `approve_role`, `application_time`, `application_by`, `approval_history`, `current_approver`

## 统一字段定义

| 字段名 | 数据类型 | 说明 | 是否必需 |
|--------|----------|------|----------|
| approval_status / status | INT | 审批状态（0-6） | 是 |
| reject_reason | VARCHAR(500) | 拒绝原因 | 否 |
| approve_by | VARCHAR(100) | 审批人姓名 | 否 |
| approve_role | VARCHAR(100) | 审批人角色 | 否 |
| application_time | DATETIME | 申请时间 | 否 |
| application_by | VARCHAR(100) | 申请人 | 否 |
| approve_time / examine_time | DATETIME | 审批时间 | 否 |
| approval_history | TEXT | 审批历史记录 | 否 |
| current_approver | VARCHAR(100) | 当前审批人 | 否 |

## 审批状态统一定义

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 通过 | 初始状态或单步通过 |
| 1 | 全部同意 | 所有审批节点都同意的最终状态 |
| 2 | 已拒绝 | 任何一个审批节点拒绝的最终状态 |
| 3 | 法诉主管审批 | 等待法诉主管审批 |
| 4 | 总监审批 | 等待总监审批 |
| 5 | 财务主管/总监抄送 | 等待财务部门确认 |
| 6 | 总经理/董事长审批(抄送) | 等待最高级别审批 |

## 迁移步骤

### 第一步：备份数据
```sql
-- 备份法诉费用表
CREATE TABLE litigation_cost_backup AS SELECT * FROM litigation_cost;

-- 备份找车费用审批表
CREATE TABLE vw_car_order_examine_backup AS SELECT * FROM vw_car_order_examine;
```

### 第二步：执行找车模块迁移
执行 `数据库迁移脚本-找车模块审批字段.sql` 文件中的SQL语句。

### 第三步：验证数据完整性
```sql
-- 检查法诉费用表字段
DESCRIBE litigation_cost;

-- 检查找车费用审批表字段
DESCRIBE vw_car_order_examine;

-- 验证审批状态数据
SELECT approval_status, COUNT(*) FROM litigation_cost GROUP BY approval_status;
SELECT status, COUNT(*) FROM vw_car_order_examine GROUP BY status;
```

### 第四步：更新应用程序
1. 重新部署后端应用
2. 更新前端页面
3. 测试审批功能

## 数据迁移注意事项

### 1. 字段类型兼容性
- **litigation_cost.approval_status**: 可能是VARCHAR类型，需要确保能正确转换为INT
- **vw_car_order_examine.status**: 应该是INT类型，与统一标准兼容

### 2. 现有数据处理
- **找车模块**: 现有的status值需要映射到新的统一状态
  - 0 (未审批) → 0 (通过)
  - 1 (已通过) → 1 (全部同意)
  - 2 (已拒绝) → 2 (已拒绝)
  - 其他 → 3 (法诉主管审批)

### 3. 索引优化
为提高查询性能，建议在以下字段上创建索引：
- `approval_status` / `status`
- `approve_by`
- `application_by`
- `application_time`

## 回滚方案

如果迁移过程中出现问题，可以执行以下回滚操作：

```sql
-- 恢复法诉费用表（如果有修改）
DROP TABLE litigation_cost;
RENAME TABLE litigation_cost_backup TO litigation_cost;

-- 恢复找车费用审批表
DROP TABLE vw_car_order_examine;
RENAME TABLE vw_car_order_examine_backup TO vw_car_order_examine;
```

## 测试验证

### 1. 功能测试
- [ ] 法诉费用审批流程测试
- [ ] 找车费用审批流程测试
- [ ] 批量审批功能测试
- [ ] 审批权限控制测试

### 2. 数据一致性测试
- [ ] 审批状态显示正确
- [ ] 审批历史记录完整
- [ ] 审批人信息准确

### 3. 性能测试
- [ ] 审批列表查询性能
- [ ] 批量操作性能
- [ ] 统计查询性能

## 常见问题

### Q1: 迁移后原有数据丢失怎么办？
A1: 迁移前已创建备份表，可以通过备份表恢复数据。

### Q2: 字段类型不匹配怎么处理？
A2: 在应用层进行类型转换，确保兼容性。

### Q3: 审批状态映射不正确怎么办？
A3: 可以通过UPDATE语句重新映射状态值。

## 联系方式

如果在迁移过程中遇到问题，请联系开发团队。

---

**重要提醒**: 
1. 在生产环境执行迁移前，请务必在测试环境完整测试
2. 执行迁移时建议在业务低峰期进行
3. 迁移完成后请及时验证功能正常性
