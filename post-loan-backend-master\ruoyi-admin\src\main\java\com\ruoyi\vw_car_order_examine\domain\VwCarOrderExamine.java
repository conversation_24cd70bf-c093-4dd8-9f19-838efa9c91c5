package com.ruoyi.vw_car_order_examine.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseApprovalEntity;

/**
 * 找车费用审批对象 vw_car_order_examine
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class VwCarOrderExamine extends BaseApprovalEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;
    private Date startTime;
    private Date endTime;
    /** 佣金 */
    @Excel(name = "佣金")
    private BigDecimal transportationFee;

    /** 拖车费 */
    @Excel(name = "拖车费")
    private BigDecimal towingFee;

    /** 贴机费 */
    @Excel(name = "贴机费")
    private BigDecimal trackerInstallationFee;

    /** 其他报销 */
    @Excel(name = "其他报销")
    private BigDecimal otherReimbursement;

    /** 合计费用 */
    @Excel(name = "合计费用")
    private BigDecimal totalCost;

    // 审批相关字段已在BaseApprovalEntity中定义，这里提供兼容性方法

    /** 兼容性方法 - 获取审批状态 */
    public Integer getStatus() {
        return super.getApprovalStatus();
    }

    /** 兼容性方法 - 设置审批状态 */
    public void setStatus(Integer status) {
        super.setApprovalStatus(status);
    }

    /** 兼容性方法 - 获取审批时间 */
    public Date getExamineTime() {
        return super.getApproveTime();
    }

    /** 兼容性方法 - 设置审批时间 */
    public void setExamineTime(Date examineTime) {
        super.setApproveTime(examineTime);
    }

    /** 找车团队（接单团队

） */
    @Excel(name = "找车团队", readConverterExp = "接=单团队")
    private Long teamId;

    /** 车库id */
    @Excel(name = "车库id")
    private Long garageId;

    /** 1-入库，2-出库 */
    @Excel(name = "1-入库，2-出库")
    private Integer libraryStatus;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inboundTime;

    /** 找车佣金 */
    @Excel(name = "找车佣金")
    private BigDecimal locatingCommission;

    /** 钥匙状态：0-未收回，1-已邮寄，2-已收回，3-未归还 */
    @Excel(name = "钥匙状态：0-未收回，1-已邮寄，2-已收回，3-未归还")
    private Long keyStatus;

    /** 收车方式：1-主动交车，2-钥匙开车，3-板车拖车 */
    @Excel(name = "收车方式：1-主动交车，2-钥匙开车，3-板车拖车")
    private Integer collectionMethod;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date allocationTime;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 客户ID */
    private String customerId;

    /** 申请ID */
    private String applyId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobilePhone;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 团队名称 */
    @Excel(name = "团队名称")
    private String teamName;

    /** 录单渠道名称 */
    @Excel(name = "录单渠道名称")
    private String jgName;

    /** 车库名称 */
    @Excel(name = "车库名称")
    private String garageName;

//    public void setId(String id)
//    {
//        this.id = id;
//    }
//
//    public String getId()
//    {
//        return id;
//    }
//
//    public void setTransportationFee(BigDecimal transportationFee)
//    {
//        this.transportationFee = transportationFee;
//    }
//
//    public BigDecimal getTransportationFee()
//    {
//        return transportationFee;
//    }
//
//    public void setTowingFee(BigDecimal towingFee)
//    {
//        this.towingFee = towingFee;
//    }
//
//    public BigDecimal getTowingFee()
//    {
//        return towingFee;
//    }
//
//    public void setTrackerInstallationFee(BigDecimal trackerInstallationFee)
//    {
//        this.trackerInstallationFee = trackerInstallationFee;
//    }
//
//    public BigDecimal getTrackerInstallationFee()
//    {
//        return trackerInstallationFee;
//    }
//
//    public void setOtherReimbursement(BigDecimal otherReimbursement)
//    {
//        this.otherReimbursement = otherReimbursement;
//    }
//
//    public BigDecimal getOtherReimbursement()
//    {
//        return otherReimbursement;
//    }
//
//    public void setTotalCost(BigDecimal totalCost)
//    {
//        this.totalCost = totalCost;
//    }
//
//    public BigDecimal getTotalCost()
//    {
//        return totalCost;
//    }
//
//    public void setStatus(Integer status)
//    {
//        this.status = status;
//    }
//
//    public Integer getStatus()
//    {
//        return status;
//    }
//
//    public void setExamineTime(Date examineTime)
//    {
//        this.examineTime = examineTime;
//    }
//
//    public Date getExamineTime()
//    {
//        return examineTime;
//    }
//
//    public void setRejectReason(String rejectReason)
//    {
//        this.rejectReason = rejectReason;
//    }
//
//    public String getRejectReason()
//    {
//        return rejectReason;
//    }
//
//    public void setTeamId(Long teamId)
//    {
//        this.teamId = teamId;
//    }
//
//    public Long getTeamId()
//    {
//        return teamId;
//    }
//
//    public void setGarageId(Long garageId)
//    {
//        this.garageId = garageId;
//    }
//
//    public Long getGarageId()
//    {
//        return garageId;
//    }
//
//    public void setLibraryStatus(Integer libraryStatus)
//    {
//        this.libraryStatus = libraryStatus;
//    }
//
//    public Integer getLibraryStatus()
//    {
//        return libraryStatus;
//    }
//
//    public void setInboundTime(Date inboundTime)
//    {
//        this.inboundTime = inboundTime;
//    }
//
//    public Date getInboundTime()
//    {
//        return inboundTime;
//    }
//
//    public void setLocatingCommission(BigDecimal locatingCommission)
//    {
//        this.locatingCommission = locatingCommission;
//    }
//
//    public BigDecimal getLocatingCommission()
//    {
//        return locatingCommission;
//    }
//
//    public void setKeyStatus(Long keyStatus)
//    {
//        this.keyStatus = keyStatus;
//    }
//
//    public Long getKeyStatus()
//    {
//        return keyStatus;
//    }
//
//    public void setCollectionMethod(Integer collectionMethod)
//    {
//        this.collectionMethod = collectionMethod;
//    }
//
//    public Integer getCollectionMethod()
//    {
//        return collectionMethod;
//    }
//
//    public void setAllocationTime(Date allocationTime)
//    {
//        this.allocationTime = allocationTime;
//    }
//
//    public Date getAllocationTime()
//    {
//        return allocationTime;
//    }
//
//    public void setCustomerName(String customerName)
//    {
//        this.customerName = customerName;
//    }
//
//    public String getCustomerName()
//    {
//        return customerName;
//    }
//
//    public void setMobilePhone(String mobilePhone)
//    {
//        this.mobilePhone = mobilePhone;
//    }
//
//    public String getMobilePhone()
//    {
//        return mobilePhone;
//    }
//
//    public void setPlateNo(String plateNo)
//    {
//        this.plateNo = plateNo;
//    }
//
//    public String getPlateNo()
//    {
//        return plateNo;
//    }
//
//    public void setTeamName(String teamName)
//    {
//        this.teamName = teamName;
//    }
//
//    public String getTeamName()
//    {
//        return teamName;
//    }
//
//    public void setGarageName(String garageName)
//    {
//        this.garageName = garageName;
//    }
//
//    public String getGarageName()
//    {
//        return garageName;
//    }
//
//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("id", getId())
//            .append("transportationFee", getTransportationFee())
//            .append("towingFee", getTowingFee())
//            .append("trackerInstallationFee", getTrackerInstallationFee())
//            .append("otherReimbursement", getOtherReimbursement())
//            .append("totalCost", getTotalCost())
//            .append("status", getStatus())
//            .append("examineTime", getExamineTime())
//            .append("rejectReason", getRejectReason())
//            .append("teamId", getTeamId())
//            .append("garageId", getGarageId())
//            .append("libraryStatus", getLibraryStatus())
//            .append("inboundTime", getInboundTime())
//            .append("locatingCommission", getLocatingCommission())
//            .append("keyStatus", getKeyStatus())
//            .append("collectionMethod", getCollectionMethod())
//            .append("allocationTime", getAllocationTime())
//            .append("customerName", getCustomerName())
//            .append("mobilePhone", getMobilePhone())
//            .append("plateNo", getPlateNo())
//            .append("teamName", getTeamName())
//            .append("garageName", getGarageName())
//            .toString();
//    }
}
