{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_account_loan\\vw_account_loan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_account_loan\\vw_account_loan\\index.vue", "mtime": *************}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICB0cmlhbF9iYWxhbmNlX3B1dCwNCiAgdHJpYWxfc3VibWl0X29yZGVyLA0KICB0cmlhbF9iYWxhbmNlX3Bvc3QsDQogIHRyaWFsX2JhbGFuY2Vfb3JkZXIsDQogIGxvYW5fcmVtaW5kZXJfb3JkZXIsDQogIGxpc3RWd19hY2NvdW50X2xvYW4sDQogIGRlbFZ3X2FjY291bnRfbG9hbiwNCiAgbG9hbl9jb21wZW5zYXRpb25fb3JkZXIsDQogIGFkZF9UcmlhbF9vcmRlciwNCiAgZGNfc3VibWl0X29yZGVyLA0KICBzdWJfVHJpYWxfb3JkZXIsDQogIHVwZGF0ZV9sb2FuX2xpc3QsDQogIGdldF9iYW5rX2FjY291bnQsDQp9IGZyb20gJ0AvYXBpL3Z3X2FjY291bnRfbG9hbi92d19hY2NvdW50X2xvYW4nDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCcNCmltcG9ydCB1c2VySW5mbyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy91c2VySW5mby52dWUnDQppbXBvcnQgY2FySW5mbyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9jYXJJbmZvLnZ1ZScNCmltcG9ydCBMb2FuUmVtaW5kZXJMb2cgZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvbG9hblJlbWluZGVyTG9nLnZ1ZScNCmltcG9ydCBMb2FuUmVtaW5kZXJMb2dTdWJtaXQgZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvbG9hblJlbWluZGVyTG9nU3VibWl0LnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgdXNlckluZm8sDQogICAgY2FySW5mbywNCiAgICBMb2FuUmVtaW5kZXJMb2csDQogICAgTG9hblJlbWluZGVyTG9nU3VibWl0LA0KICB9LA0KICBwcm9wczogew0KICAgIHZhbHVlOiBbU3RyaW5nLCBPYmplY3QsIEFycmF5XSwNCiAgICAvLyDkuIrkvKDmjqXlj6PlnLDlnYANCiAgICBhY3Rpb246IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIC8vIGRlZmF1bHQ6ICIvY29tbW9uL3VwbG9hZCINCiAgICAgIGRlZmF1bHQ6ICcvY29tbW9uL29zc3VwbG9hZCcsDQogICAgfSwNCiAgICAvLyDkuIrkvKDmkLrluKbnmoTlj4LmlbANCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgfSwNCiAgfSwNCiAgbmFtZTogJ1Z3X2FjY291bnRfbG9hbicsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaOp+WItuabtOWkmuetm+mAieadoeS7tuaYvuekug0KICAgICAgc2hvd01vcmU6IGZhbHNlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIFZJRVfooajmoLzmlbDmja4NCiAgICAgIHZ3X2FjY291bnRfbG9hbkxpc3Q6IFtdLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBsb2dvcGVuOiBmYWxzZSwgLy/lgqzorrDml6Xlv5cNCiAgICAgIGN1cnJlbnRSb3c6IHt9LCAvL2RpYWxvZ+S8oOWFpeaVsOaNrg0KICAgICAgdXJnZUJhY2tvcGVuOiBmYWxzZSwgLy/lgqzlm57nu5PmuIUNCiAgICAgIGRlcmF0ZW9wZW46IGZhbHNlLCAvL+WHj+WFjee7k+a4hQ0KICAgICAgY29tbXV0ZW9wZW46IGZhbHNlLCAvL+WPkei1t+S7o+WBvw0KICAgICAgZGV0YWlsU2hvdzogZmFsc2UsIC8v5p+l55yL5pel5b+X6K+m5oOFDQogICAgICBsZW5kZXJTaG93OiBmYWxzZSwgLy/otLfmrL7kurrkv6Hmga8NCiAgICAgIGN1c3RvbWVySW5mbzogew0KICAgICAgICBjdXN0b21lcklkOiAnJywNCiAgICAgICAgYXBwbHlJZDogJycsDQogICAgICB9LA0KICAgICAgY2FyU2hvdzogZmFsc2UsIC8vIOi9pui+huS/oeaBrw0KICAgICAgcGxhdGVObzogJycsIC8vIOi9pui+hue8luWPtw0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTUsDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgY2VydElkOiBudWxsLA0KICAgICAgICBwbGF0ZU5vOiBudWxsLA0KICAgICAgICBwYXJ0bmVySWQ6IG51bGwsDQogICAgICAgIGpnTmFtZTogbnVsbCwNCiAgICAgICAgc2xpcHBhZ2VTdGF0dXM6IG51bGwsDQogICAgICAgIGZvbGxvd1N0YXR1czogbnVsbCwNCiAgICAgICAgZm9sbG93VXA6IG51bGwsDQogICAgICAgIGFsbG9jYXRpb25UaW1lOiBudWxsLA0KICAgICAgICBzdGFydFRpbWU6ICcnLA0KICAgICAgICBlbmRUaW1lOiAnJywNCiAgICAgICAgaXNFeHRlbnNpb246ICcnLA0KICAgICAgICBjYXJTdGF0dXM6ICcnLA0KICAgICAgICBpc0ZpbmRDYXI6ICcnLA0KICAgICAgfSwNCg0KICAgICAgYmFua0xpc3Q6IFsNCiAgICAgICAgeyB2YWx1ZTogJ0VPMDAwMDAwMTAnLCBsYWJlbDogJ+iLj+mTtumHkeennycgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDYnLCBsYWJlbDogJ+a1meWVhumTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDcnLCBsYWJlbDogJ+S4reWFs+adkemTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDgnLCBsYWJlbDogJ+iTnea1t+mTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDknLCBsYWJlbDogJ+WNjueRnumTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMTAnLCBsYWJlbDogJ+ealuaWsOenn+i1gScgfSwNCiAgICAgIF0sDQoNCiAgICAgIGlzRXh0ZW5zaW9uTGlzdDogWw0KICAgICAgICB7IGxhYmVsOiAn5ZCmJywgdmFsdWU6ICcwJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5pivJywgdmFsdWU6ICcxJyB9LA0KICAgICAgXSwNCiAgICAgIHNsaXBwYWdlTGlzdDogWw0KICAgICAgICB7IGxhYmVsOiAn5o+Q6YaSJywgdmFsdWU6IDEgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eUteWCrCcsIHZhbHVlOiAyIH0sDQogICAgICAgIHsgbGFiZWw6ICfkuIrorr8nLCB2YWx1ZTogMyB9LA0KICAgICAgICB7IGxhYmVsOiAn6YC+5pyfMzAtNjAnLCB2YWx1ZTogNCB9LA0KICAgICAgICB7IGxhYmVsOiAn6YC+5pyfNjArJywgdmFsdWU6IDUgfSwNCiAgICAgIF0sDQogICAgICBmb2xsb3dVcExpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+e7p+e7reiBlOezuycsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICfnuqblrprov5jmrL4nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn5peg5rOV6Lef6L+bJywgdmFsdWU6IDMgfSwNCiAgICAgIF0sDQogICAgICBhY2NvdW50TGlzdDogW10sIC8v6ZO26KGM6LSm5oi35YiX6KGoDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fSwNCiAgICAgIGRpYWxvZ0ltYWdlVXJsOiAnJywNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgdGV4dGFyZWE6IG51bGwsDQogICAgICB1cGxvYWRJbWdVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyB0aGlzLmFjdGlvbiwgLy8g5LiK5Lyg55qE5Zu+54mH5pyN5Yqh5Zmo5Zyw5Z2ADQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCksDQogICAgICB9LA0KICAgICAgcmVtaW5kZXJMaXN0OiBbXSwgLy/lgqzorrDliJfooagNCiAgICAgIHJlbWluZGVyU2hvdzogZmFsc2UsDQogICAgICB1cmdlZm9ybToge30sDQogICAgICBkZXJhdGVmb3JtOiB7fSwNCiAgICAgIHJhZGlvOiAwLA0KICAgICAgbG9nTGlzdDogW10sIC8v5YKs6K6w5pel5b+XDQogICAgICBsb2dEZXRhaWw6IHt9LCAvL+WCrOiusOaXpeW/l+ivpuaDhQ0KICAgICAgbG9nRm9ybTogew0KICAgICAgICBsb2FuSWQ6IG51bGwsDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxNSwNCiAgICAgIH0sDQogICAgICBsb2d0b3RhbDogMCwNCiAgICAgIHVyZ2VBbGxNb25leTogMCwgLy/lgqzlm57nu5PmuIXmgLvmrKDmrL4NCiAgICAgIGRlcmF0ZUFsbE1vbmV5OiAwLCAvL+WHj+WFjee7k+a4heaAu+asoOasvg0KICAgICAgbG9hbklkOiBudWxsLA0KICAgICAgYWRkSWQ6IG51bGwsIC8v5YKs5Zue44CB5YeP5YWN5paw5aKeaWQNCiAgICAgIGptRm9ybToge30sDQogICAgICBjaEZvcm06IHt9LA0KICAgICAgdXJnZWZvcm1idG90YWxNb25leTogMCwNCiAgICAgIHVyZ2Vmb3JtZHRvdGFsTW9uZXk6IDAsDQogICAgICB1cmdlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzOiAwLA0KICAgICAgdXJnZWZvcm1vbmVDb21tdXRhdGlvbjogMCwNCiAgICAgIGRlcmF0ZWZvcm1idG90YWxNb25leTogMCwNCiAgICAgIGRlcmF0ZWZvcm1kdG90YWxNb25leTogMCwNCiAgICAgIGRlcmF0ZWZvcm1saXF1aWRhdGVkRGFtYWdlczogMCwNCiAgICAgIGRlcmF0ZWZvcm1vbmVDb21tdXRhdGlvbjogMCwNCiAgICAgIHRyaWFsRm9ybToge30sDQogICAgICB0cmlhbEZvcm1wcmluY2lwYWw6IDAsDQogICAgICB0cmlhbEZvcm1ib3ZlcmR1ZUFtb3VudDogMCwNCiAgICAgIHRyaWFsRm9ybWludGVyZXN0OiAwLA0KICAgICAgdHJpYWxGb3JtYWxsOiAwLA0KICAgICAgdHJpYWxGb3JtZG92ZXJkdWVBbW91bnQ6IDAsDQogICAgICB0cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlczogMCwNCiAgICAgIHRyaWFsRm9ybXRvdGFsOiAwLA0KICAgICAgdHJpYWxGb3Jtb3RoZXJEZWJ0OiAwLA0KICAgICAgZGt5cU1vbmV5OiAwLA0KICAgICAgYmFua3lxTW9uZXk6IDAsDQogICAgICBsZW5kaW5nQmFuazogbnVsbCwNCiAgICAgIE9yZGVyaW5nQ2hhbm5lbDogbnVsbCwNCiAgICAgIGxvZ0xvYW5JZDogbnVsbCwgLy/ltYzlhaXlgqzorrDml6Xlv5fnu4Tku7bnmoRsb2FuSWQNCiAgICAgIHN1Ym1pdExvYW5JZDogbnVsbCwgLy/ltYzlhaXmj5DkuqTlgqzorrDnu4Tku7bnmoRsb2FuSWQNCiAgICAgIGNhclN0YXR1c0xpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+ecgeWGheato+W4uOihjOmpticsIHZhbHVlOiAnMScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+ecgeWkluato+W4uOihjOmpticsIHZhbHVlOiAnMicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+aKteaKvCcsIHZhbHVlOiAnMycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eWkeS8vOaKteaKvCcsIHZhbHVlOiAnNCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eWkeS8vOm7kei9picsIHZhbHVlOiAnNScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suWFpeW6kycsIHZhbHVlOiAnNicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+i9puWcqOazlemZoicsIHZhbHVlOiAnNycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suazleaLjScsIHZhbHVlOiAnOCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WNj+WVhuWNlui9picsIHZhbHVlOiAnOScgfSwNCiAgICAgIF0sDQogICAgICBpc0ZpbmRDYXJMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfmnKrmtL7ljZUnLCB2YWx1ZTogJzAnIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7LmtL7ljZUnLCB2YWx1ZTogJzEnIH0sDQogICAgICBdLA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMuZ2V0QWNjb3VudExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0QWNjb3VudExpc3QoKSB7DQogICAgICBnZXRfYmFua19hY2NvdW50KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYWNjb3VudExpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRXN0aW1hdGVCYWREZWJ0KHJvdykgew0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgaWQ6IHJvdy5sb2FuSWQsDQogICAgICAgIGJhZERlYnQ6IDEsDQogICAgICB9DQogICAgICB1cGRhdGVfbG9hbl9saXN0KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfpooTkvLDlkYbotKbmiJDlip8nKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfpooTkvLDlkYbotKblpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgdHJpYWxTdWIoKSB7DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgYXBwbHlJZDogdGhpcy50cmlhbEZvcm0uYXBwbHlJZCwNCiAgICAgICAgaWQ6IHRoaXMudHJpYWxGb3JtLmlkLA0KICAgICAgICBsb2FuSWQ6IHRoaXMudHJpYWxGb3JtLmxvYW5JZCwNCiAgICAgICAgbG9hbkFtb3VudDogdGhpcy50cmlhbEZvcm0ubG9hbkFtb3VudCwNCiAgICAgICAgcGFydG5lcklkOiB0aGlzLnRyaWFsRm9ybS5wYXJ0bmVySWQsDQogICAgICB9DQogICAgICBkY19zdWJtaXRfb3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudHJpYWxGb3JtcHJpbmNpcGFsID0gcmVzcG9uc2UuZGF0YS5wcmluY2lwYWwgfHwgMA0KICAgICAgICB0aGlzLnRyaWFsRm9ybS5kZWZhdWx0SW50ZXJlc3QgPSByZXNwb25zZS5kYXRhLmRlZmF1bHRJbnRlcmVzdCB8fCAwDQogICAgICAgIHRoaXMudHJpYWxGb3JtaW50ZXJlc3QgPSByZXNwb25zZS5kYXRhLmludGVyZXN0IHx8IDANCiAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgPSByZXNwb25zZS5kYXRhLmJ0b3RhbE1vbmV5IHx8IDANCiAgICAgICAgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCA9IHJlc3BvbnNlLmRhdGEuZHRvdGFsTW9uZXkgfHwgMA0KICAgICAgICB0aGlzLnRyaWFsRm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS5saXF1aWRhdGVkRGFtYWdlcyB8fCAwDQogICAgICAgIHRoaXMudHJpYWxGb3JtdG90YWwgPSBOdW1iZXIoDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgKyB0aGlzLnRyaWFsRm9ybWRvdmVyZHVlQW1vdW50ICsgdGhpcy50cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlcyArIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0DQogICAgICAgICkudG9GaXhlZCgyKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHN1Ym1pdERlcmF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmRlcmF0ZWZvcm1idG90YWxNb25leSAmJiAhdGhpcy5kZXJhdGVmb3JtLmFjY291bnROdW1iZXIxKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6npk7booYznu5PmuIXotKblj7cnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmRlcmF0ZWZvcm1kdG90YWxNb25leSAmJiAhdGhpcy5kZXJhdGVmb3JtLmFjY291bnROdW1iZXIyKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nku6PmiaPliankvZnotKblj7cnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmRlcmF0ZWZvcm1saXF1aWRhdGVkRGFtYWdlcyAmJiAhdGhpcy5kZXJhdGVmb3JtLmFjY291bnROdW1iZXIzKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nov53nuqbph5HotKblj7cnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmRlcmF0ZWZvcm0ub3RoZXJEZWJ0ICYmICF0aGlzLmRlcmF0ZWZvcm0uYWNjb3VudE51bWJlcjQpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeWFtuS7luasoOasvui0puWPtycpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuZGVyYXRlZm9ybW9uZUNvbW11dGF0aW9uICYmICF0aGlzLmRlcmF0ZWZvcm0uYWNjb3VudE51bWJlcjUpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeWNleacn+S7o+WBv+mHkei0puWPtycpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdmFyIGRhdGEgPSB7DQogICAgICAgIGN1c3RvbWVyTmFtZTogdGhpcy5kZXJhdGVmb3JtLmN1c3RvbWVyTmFtZSwNCiAgICAgICAgb3JnTmFtZTogdGhpcy5kZXJhdGVmb3JtLm9yZ05hbWUsDQogICAgICAgIGJhbms6IHRoaXMuZGVyYXRlZm9ybS5iYW5rLA0KICAgICAgICBsb2FuQW1vdW50OiB0aGlzLmRlcmF0ZWZvcm0ubG9hbkFtb3VudCwNCiAgICAgICAgYnRvdGFsTW9uZXk6IHRoaXMuZGVyYXRlZm9ybS5idG90YWxNb25leSwNCiAgICAgICAgYWNjb3VudE51bWJlcjE6IHRoaXMuZGVyYXRlZm9ybS5hY2NvdW50TnVtYmVyMSwNCiAgICAgICAgZHRvdGFsTW9uZXk6IHRoaXMuZGVyYXRlZm9ybS5kdG90YWxNb25leSwNCiAgICAgICAgYWNjb3VudE51bWJlcjI6IHRoaXMuZGVyYXRlZm9ybS5hY2NvdW50TnVtYmVyMiwNCiAgICAgICAgbGlxdWlkYXRlZERhbWFnZXM6IHRoaXMuZGVyYXRlZm9ybS5saXF1aWRhdGVkRGFtYWdlcywNCiAgICAgICAgYWNjb3VudE51bWJlcjM6IHRoaXMuZGVyYXRlZm9ybS5hY2NvdW50TnVtYmVyMywNCiAgICAgICAgb3RoZXJEZWJ0OiB0aGlzLmRlcmF0ZWZvcm0ub3RoZXJEZWJ0LA0KICAgICAgICBhY2NvdW50TnVtYmVyNDogdGhpcy5kZXJhdGVmb3JtLmFjY291bnROdW1iZXI0LA0KICAgICAgICBvbmVDb21tdXRhdGlvbjogdGhpcy5kZXJhdGVmb3JtLm9uZUNvbW11dGF0aW9uLA0KICAgICAgICBhY2NvdW50TnVtYmVyNTogdGhpcy5kZXJhdGVmb3JtLmFjY291bnROdW1iZXI1LA0KICAgICAgICB0b3RhbE1vbmV5OiB0aGlzLmRlcmF0ZUFsbE1vbmV5LA0KICAgICAgICBpZDogdGhpcy5hZGRJZCwNCiAgICAgICAgc3RhdHVzOiAyLA0KICAgICAgICBleGFtaW5lU3RhdHVzOiAxLA0KICAgICAgfQ0KICAgICAgdHJpYWxfYmFsYW5jZV9wdXQoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+aPkOS6pOaIkOWKnycpDQogICAgICAgICAgdGhpcy5kZXJhdGVvcGVuID0gZmFsc2UNCiAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0gPSB7fQ0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybWJ0b3RhbE1vbmV5ID0gMA0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybWR0b3RhbE1vbmV5ID0gMA0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gMA0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybW9uZUNvbW11dGF0aW9uID0gMA0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc3VibWl0VXJnZSgpIHsNCiAgICAgIGlmICh0aGlzLnVyZ2Vmb3JtYnRvdGFsTW9uZXkgJiYgIXRoaXMudXJnZWZvcm0uYWNjb3VudE51bWJlcjEpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqemTtuihjOe7k+a4hei0puWPtycpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMudXJnZWZvcm1kdG90YWxNb25leSAmJiAhdGhpcy51cmdlZm9ybS5hY2NvdW50TnVtYmVyMikgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup5Luj5omj5Ymp5L2Z6LSm5Y+3JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAodGhpcy51cmdlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzICYmICF0aGlzLnVyZ2Vmb3JtLmFjY291bnROdW1iZXIzKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nov53nuqbph5HotKblj7cnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnVyZ2Vmb3JtLm90aGVyRGVidCAmJiAhdGhpcy51cmdlZm9ybS5hY2NvdW50TnVtYmVyNCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup5YW25LuW5qyg5qy+6LSm5Y+3JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAodGhpcy51cmdlZm9ybW9uZUNvbW11dGF0aW9uICYmICF0aGlzLnVyZ2Vmb3JtLmFjY291bnROdW1iZXI1KSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nljZXmnJ/ku6Plgb/ph5HotKblj7cnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHZhciBkYXRhID0gew0KICAgICAgICBjdXN0b21lck5hbWU6IHRoaXMudXJnZWZvcm0uY3VzdG9tZXJOYW1lLA0KICAgICAgICBvcmdOYW1lOiB0aGlzLnVyZ2Vmb3JtLm9yZ05hbWUsDQogICAgICAgIGJhbms6IHRoaXMudXJnZWZvcm0uYmFuaywNCiAgICAgICAgbG9hbkFtb3VudDogdGhpcy51cmdlZm9ybS5sb2FuQW1vdW50LA0KICAgICAgICBidG90YWxNb25leTogdGhpcy51cmdlZm9ybS5idG90YWxNb25leSwNCiAgICAgICAgYWNjb3VudE51bWJlcjE6IHRoaXMudXJnZWZvcm0uYWNjb3VudE51bWJlcjEsDQogICAgICAgIGR0b3RhbE1vbmV5OiB0aGlzLnVyZ2Vmb3JtLmR0b3RhbE1vbmV5LA0KICAgICAgICBhY2NvdW50TnVtYmVyMjogdGhpcy51cmdlZm9ybS5hY2NvdW50TnVtYmVyMiwNCiAgICAgICAgbGlxdWlkYXRlZERhbWFnZXM6IHRoaXMudXJnZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMsDQogICAgICAgIGFjY291bnROdW1iZXIzOiB0aGlzLnVyZ2Vmb3JtLmFjY291bnROdW1iZXIzLA0KICAgICAgICBvdGhlckRlYnQ6IHRoaXMudXJnZWZvcm0ub3RoZXJEZWJ0LA0KICAgICAgICBhY2NvdW50TnVtYmVyNDogdGhpcy51cmdlZm9ybS5hY2NvdW50TnVtYmVyNCwNCiAgICAgICAgb25lQ29tbXV0YXRpb246IHRoaXMudXJnZWZvcm0ub25lQ29tbXV0YXRpb24sDQogICAgICAgIGFjY291bnROdW1iZXI1OiB0aGlzLnVyZ2Vmb3JtLmFjY291bnROdW1iZXI1LA0KICAgICAgICB0b3RhbE1vbmV5OiB0aGlzLnVyZ2VBbGxNb25leSwNCiAgICAgICAgaWQ6IHRoaXMuYWRkSWQsDQogICAgICAgIHN0YXR1czogMSwNCiAgICAgICAgZXhhbWluZVN0YXR1czogMSwNCiAgICAgIH0NCiAgICAgIHRyaWFsX2JhbGFuY2VfcHV0KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmj5DkuqTmiJDlip8nKQ0KICAgICAgICAgIHRoaXMudXJnZUJhY2tvcGVuID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnVyZ2Vmb3JtID0ge30NCiAgICAgICAgICB0aGlzLnVyZ2Vmb3JtYnRvdGFsTW9uZXkgPSAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybWR0b3RhbE1vbmV5ID0gMA0KICAgICAgICAgIHRoaXMudXJnZWZvcm1saXF1aWRhdGVkRGFtYWdlcyA9IDANCiAgICAgICAgICB0aGlzLnVyZ2Vmb3Jtb25lQ29tbXV0YXRpb24gPSAwDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBvbklucHV0TGltaXQoa2V5LCB2YWx1ZSwgbWluID0gMCwgbWF4ID0gMTAwKSB7DQogICAgICBsZXQgdmFsID0gTnVtYmVyKHZhbHVlKQ0KICAgICAgaWYgKGlzTmFOKHZhbCkpIHZhbCA9IG1pbg0KICAgICAgaWYgKHZhbCA+IG1heCkgdmFsID0gbWF4DQogICAgICBpZiAodmFsIDwgbWluKSB2YWwgPSBtaW4NCiAgICAgIHRoaXMudHJpYWxGb3JtW2tleV0gPSBOdW1iZXIodmFsKQ0KICAgIH0sDQogICAgY2FuY2VsRGV0YWlsKCkgew0KICAgICAgdGhpcy5kZXRhaWxTaG93ID0gZmFsc2UNCiAgICB9LA0KICAgIC8v5p+l55yL6L2m54mM5L+h5oGvDQogICAgY2hlY2tDYXIocGxhdGVObykgew0KICAgICAgdGhpcy5wbGF0ZU5vID0gcGxhdGVObw0KICAgICAgdGhpcy5jYXJTaG93ID0gdHJ1ZQ0KICAgIH0sDQogICAgLy/mn6XnnIvotLfmrL7kurrkv6Hmga8NCiAgICBjaGVja0xlbmRlcihjdXN0b21lckluZm8pIHsNCiAgICAgIHRoaXMuY3VzdG9tZXJJbmZvID0gY3VzdG9tZXJJbmZvDQogICAgICB0aGlzLmxlbmRlclNob3cgPSB0cnVlDQogICAgfSwNCiAgICBwbGFuQnRoKHJvdykgew0KICAgICAgdmFyIGRhdGEgPSB7DQogICAgICAgIHBhcnRuZXJJZDogcm93LnBhcnRuZXJJZCwNCiAgICAgICAgYXBwbHlJZDogcm93LmFwcGx5SWQsDQogICAgICB9DQogICAgICBpZiAocm93LnBhcnRuZXJJZCA9PSAnSU8wMDAwMDAwOCcpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICcvcmVwYXltZW50L3JlcGF5bWVudF9wbGFuL2xoaW5kZXgnLA0KICAgICAgICAgIHF1ZXJ5OiBkYXRhLA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIGlmIChyb3cucGFydG5lcklkID09ICdFTzAwMDAwMDEwJykgew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgcGF0aDogJy9yZXBheW1lbnQvcmVwYXltZW50X3BsYW4vc3lpbmRleCcsDQogICAgICAgICAgcXVlcnk6IGRhdGEsDQogICAgICAgIH0pDQogICAgICB9IGVsc2UgaWYgKHJvdy5wYXJ0bmVySWQgPT0gJ0lPMDAwMDAwMDYnKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICBwYXRoOiAnL3JlcGF5bWVudC9yZXBheW1lbnRfcGxhbi96c2luZGV4JywNCiAgICAgICAgICBxdWVyeTogZGF0YSwNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSBpZiAocm93LnBhcnRuZXJJZCA9PSAnSU8wMDAwMDAwNycpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICcvcmVwYXltZW50L3JlcGF5bWVudF9wbGFuL3pnY2luZGV4JywNCiAgICAgICAgICBxdWVyeTogZGF0YSwNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSBpZiAocm93LnBhcnRuZXJJZCA9PSAnSU8wMDAwMDAwOScpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICcvcmVwYXltZW50L3JlcGF5bWVudF9wbGFuL2hyaW5kZXgnLA0KICAgICAgICAgIHF1ZXJ5OiBkYXRhLA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICcvcmVwYXltZW50L3JlcGF5bWVudF9wbGFuL3d4aW5kZXgnLA0KICAgICAgICAgIHF1ZXJ5OiBkYXRhLA0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2hlY2tSZW1pbmRlcihpZCkgew0KICAgICAgdmFyIGRhdGEgPSB7DQogICAgICAgIGxvYW5JZDogaWQsDQogICAgICAgIHVyZ2VTdGF0dXM6IDIsDQogICAgICB9DQogICAgICBsb2FuX3JlbWluZGVyX29yZGVyKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlbWluZGVyTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy5yZW1pbmRlclNob3cgPSB0cnVlDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBjYW5jZWxjb21tdXRlKCkgew0KICAgICAgdGhpcy5jb21tdXRlb3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBpbml0aWF0ZShyb3cpIHsNCiAgICAgIC8v5Y+R6LW35Luj5YG/DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgbG9hbklkOiByb3cubG9hbklkLA0KICAgICAgfQ0KICAgICAgbG9hbl9jb21wZW5zYXRpb25fb3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy50cmlhbEZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgdGhpcy50cmlhbEZvcm0ubG9hbkFtb3VudCA9IHJvdy5jb250cmFjdEFtdCB8fCAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm0uZHRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtcHJpbmNpcGFsID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5wcmluY2lwYWwgOiAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1ib3ZlcmR1ZUFtb3VudCA9IHJvdy5ib3ZlcmR1ZUFtb3VudCB8fCAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1pbnRlcmVzdCA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UuaW50ZXJlc3QgOiAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmJ0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtZG92ZXJkdWVBbW91bnQgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtbGlxdWlkYXRlZERhbWFnZXMgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmxpcXVpZGF0ZWREYW1hZ2VzIDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0ID0gcmVzcG9uc2UuZGF0YS5vdGhlckRlYnQgPyByZXNwb25zZS5kYXRhLm90aGVyRGVidCA6IDANCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnRyaWFsRm9ybWFsbCwgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCwgdGhpcy50cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlcywgdGhpcy50cmlhbEZvcm1vdGhlckRlYnQpDQogICAgICAgICAgdGhpcy50cmlhbEZvcm10b3RhbCA9IE51bWJlcigNCiAgICAgICAgICAgIHRoaXMudHJpYWxGb3JtYWxsICsgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCArIHRoaXMudHJpYWxGb3JtbGlxdWlkYXRlZERhbWFnZXMgKyB0aGlzLnRyaWFsRm9ybW90aGVyRGVidA0KICAgICAgICAgICkudG9GaXhlZCgyKQ0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtLmltYWdlID0gcmVzcG9uc2UuZGF0YS5pbWFnZSA/IHJlc3BvbnNlLmRhdGEuaW1hZ2Uuc3BsaXQoJywnKSA6IFtdDQoNCiAgICAgICAgICB0aGlzLmNvbW11dGVvcGVuID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYWRkVHJpYWwocm93KQ0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtLmltYWdlID0gW10NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFkZFRyaWFsKHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93KQ0KICAgICAgdmFyIGRhdGEgPSB7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIGFwcGx5SWQ6IHJvdy5hcHBseUlkLA0KICAgICAgICBsb2FuSWQ6IHJvdy5sb2FuSWQsDQogICAgICAgIGN1c3RvbWVySWQ6IHJvdy5jdXN0b21lcklkLA0KICAgICAgICBjdXN0b21lck5hbWU6IHJvdy5jdXN0b21lck5hbWUsDQogICAgICAgIHNhbGVzbWFuOiByb3cubmlja05hbWUsDQogICAgICAgIG9yZ05hbWU6IHJvdy5qZ05hbWUsDQogICAgICAgIHBhcnRuZXJJZDogcm93LnBhcnRuZXJJZCwNCiAgICAgICAgYmFuazogcm93Lm9yZ05hbWUsDQogICAgICAgIGxvYW5BbW91bnQ6IHJvdy5jb250cmFjdEFtdCwNCiAgICAgICAgZXhhbWluZVN0YXR1czogMCwNCiAgICAgIH0NCiAgICAgIGFkZF9UcmlhbF9vcmRlcihkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50cmlhbEZvcm0gPSByZXNwb25zZS5kYXRhIHx8IHt9DQogICAgICAgIHRoaXMuY29tbXV0ZW9wZW4gPSB0cnVlDQogICAgICB9KQ0KICAgIH0sDQogICAgY2FuY2VsZGVyYXRlKCkgew0KICAgICAgdGhpcy5kZXJhdGVmb3JtID0ge30NCiAgICAgIHRoaXMuam1Gb3JtID0ge30NCiAgICAgIHRoaXMuZGVyYXRlZm9ybWJ0b3RhbE1vbmV5ID0gMA0KICAgICAgdGhpcy5kZXJhdGVmb3JtZHRvdGFsTW9uZXkgPSAwDQogICAgICB0aGlzLmRlcmF0ZWZvcm1saXF1aWRhdGVkRGFtYWdlcyA9IDANCiAgICAgIHRoaXMuZGVyYXRlZm9ybW9uZUNvbW11dGF0aW9uID0gMA0KICAgICAgdGhpcy5kZXJhdGVBbGxNb25leSA9IDANCiAgICAgIHRoaXMuZGVyYXRlb3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBoYW5kbGVJbnB1dDModmFsdWUpIHsNCiAgICAgIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0ID0gTnVtYmVyKHZhbHVlKQ0KICAgICAgdGhpcy50cmlhbEZvcm10b3RhbCA9IE51bWJlcigNCiAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgKyB0aGlzLnRyaWFsRm9ybWRvdmVyZHVlQW1vdW50ICsgdGhpcy50cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlcyArIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0DQogICAgICApLnRvRml4ZWQoMikNCiAgICB9LA0KICAgIGhhbmRsZUlucHV0Mih2YWx1ZSkgew0KICAgICAgdGhpcy5kZXJhdGVmb3JtLm90aGVyRGVidCA9IE51bWJlcih2YWx1ZSkNCiAgICAgIHRoaXMuZGVyYXRlQWxsTW9uZXkgPSBOdW1iZXIoDQogICAgICAgIHRoaXMuZGVyYXRlZm9ybS5idG90YWxNb25leSArDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmR0b3RhbE1vbmV5ICsNCiAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMgKw0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5vdGhlckRlYnQgKw0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5vbmVDb21tdXRhdGlvbg0KICAgICAgKS50b0ZpeGVkKDIpDQogICAgfSwNCiAgICBoYW5kbGVJbnB1dDEodmFsdWUpIHsNCiAgICAgIHRoaXMudXJnZWZvcm0ub3RoZXJEZWJ0ID0gTnVtYmVyKHZhbHVlKQ0KICAgICAgdGhpcy51cmdlQWxsTW9uZXkgPSBOdW1iZXIoDQogICAgICAgIHRoaXMudXJnZWZvcm0uYnRvdGFsTW9uZXkgKw0KICAgICAgICAgIHRoaXMudXJnZWZvcm0uZHRvdGFsTW9uZXkgKw0KICAgICAgICAgIHRoaXMudXJnZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMgKw0KICAgICAgICAgIHRoaXMudXJnZWZvcm0ub3RoZXJEZWJ0ICsNCiAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLm9uZUNvbW11dGF0aW9uDQogICAgICApLnRvRml4ZWQoMikNCiAgICB9LA0KICAgIGRlcmF0ZVNldHRsZShyb3cpIHsNCiAgICAgIHZhciBkYXRhID0gew0KICAgICAgICBsb2FuSWQ6IHJvdy5sb2FuSWQsDQogICAgICAgIHN0YXR1czogMiwNCiAgICAgICAgcGFnZVNpemU6IDEsDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICB9DQogICAgICB0aGlzLmxvYW5JZCA9IHJvdy5sb2FuSWQNCiAgICAgIHRyaWFsX2JhbGFuY2Vfb3JkZXIoZGF0YSkNCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEuY3VzdG9tZXJOYW1lKSB7DQogICAgICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5jdXN0b21lck5hbWUgPSByb3cuY3VzdG9tZXJOYW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEub3JnTmFtZSkgew0KICAgICAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0ub3JnTmFtZSA9IHJvdy5qZ05hbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICghcmVzcG9uc2UuZGF0YS5iYW5rKSB7DQogICAgICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5iYW5rID0gcm93Lm9yZ05hbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICghcmVzcG9uc2UuZGF0YS5sb2FuQW1vdW50KSB7DQogICAgICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5sb2FuQW1vdW50ID0gcm93LmNvbnRyYWN0QW10DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0uYnRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmJ0b3RhbE1vbmV5IDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmR0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5kdG90YWxNb25leSA6IDANCiAgICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5vbmVDb21tdXRhdGlvbiA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2Uub25lQ29tbXV0YXRpb24gOiAwDQogICAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmxpcXVpZGF0ZWREYW1hZ2VzIDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtYnRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmJ0b3RhbE1vbmV5IDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtZHRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3Jtb25lQ29tbXV0YXRpb24gPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLm9uZUNvbW11dGF0aW9uIDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtbGlxdWlkYXRlZERhbWFnZXMgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmxpcXVpZGF0ZWREYW1hZ2VzIDogMA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVBbGxNb25leSA9IE51bWJlcigNCiAgICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmJ0b3RhbE1vbmV5ICsgdGhpcy5kZXJhdGVmb3JtLmR0b3RhbE1vbmV5ICsgdGhpcy5kZXJhdGVmb3JtLmxpcXVpZGF0ZWREYW1hZ2VzICsgdGhpcy5kZXJhdGVmb3JtLm9uZUNvbW11dGF0aW9uDQogICAgICAgICAgICApLnRvRml4ZWQoMikNCiAgICAgICAgICAgIHRoaXMuYWRkSWQgPSByZXNwb25zZS5kYXRhLmlkDQogICAgICAgICAgICB0aGlzLmptRm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5wb3N0VHJpYWwocm93LCAyKQ0KICAgICAgICAgICAgLy8gdGhpcy5kZXJhdGVmb3JtID0ge30NCiAgICAgICAgICAgIC8vIHRoaXMuZGVyYXRlZm9ybS5jdXN0b21lck5hbWUgPSByb3cuY3VzdG9tZXJOYW1lDQogICAgICAgICAgICAvLyB0aGlzLmRlcmF0ZWZvcm0ub3JnTmFtZSA9IHJvdy5qZ05hbWUNCiAgICAgICAgICAgIC8vIHRoaXMuZGVyYXRlZm9ybS5iYW5rID0gcm93Lm9yZ05hbWUNCiAgICAgICAgICAgIC8vIHRoaXMuZGVyYXRlZm9ybS5sb2FuQW1vdW50ID0gcm93LmNvbnRyYWN0QW10DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goZXJyb3IgPT4ge30pDQogICAgICB0aGlzLmRlcmF0ZW9wZW4gPSB0cnVlDQogICAgfSwNCiAgICBjYW5jZWx1cmdlQmFjaygpIHsNCiAgICAgIHRoaXMudXJnZWZvcm0gPSB7fQ0KICAgICAgdGhpcy5jaEZvcm0gPSB7fQ0KICAgICAgdGhpcy51cmdlZm9ybWJ0b3RhbE1vbmV5ID0gMA0KICAgICAgdGhpcy51cmdlZm9ybWR0b3RhbE1vbmV5ID0gMA0KICAgICAgdGhpcy51cmdlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gMA0KICAgICAgdGhpcy51cmdlZm9ybW9uZUNvbW11dGF0aW9uID0gMA0KICAgICAgdGhpcy51cmdlQWxsTW9uZXkgPSAwDQogICAgICB0aGlzLnVyZ2VCYWNrb3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBnZXRVcmdlTW9uZXkoZSkgew0KICAgICAgdmFyIGRhdGEgPSB7fQ0KICAgICAgaWYgKGUgPT0gMSkgew0KICAgICAgICBkYXRhID0gdGhpcy5jaEZvcm0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGRhdGEgPSB0aGlzLmptRm9ybQ0KICAgICAgfQ0KICAgICAgdHJpYWxfc3VibWl0X29yZGVyKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAoZSA9PSAxKSB7DQogICAgICAgICAgLy8gdGhpcy51cmdlZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLmJ0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS5idG90YWxNb25leSB8fCAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybS5kdG90YWxNb25leSA9IHJlc3BvbnNlLmRhdGEuZHRvdGFsTW9uZXkgfHwgMA0KICAgICAgICAgIHRoaXMudXJnZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMgPSByZXNwb25zZS5kYXRhLmxpcXVpZGF0ZWREYW1hZ2VzIHx8IDANCiAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLm9uZUNvbW11dGF0aW9uID0gcmVzcG9uc2UuZGF0YS5vbmVDb21tdXRhdGlvbiB8fCAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybWJ0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS5idG90YWxNb25leSB8fCAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybWR0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS5kdG90YWxNb25leSB8fCAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS5saXF1aWRhdGVkRGFtYWdlcyB8fCAwDQogICAgICAgICAgdGhpcy51cmdlZm9ybW9uZUNvbW11dGF0aW9uID0gcmVzcG9uc2UuZGF0YS5vbmVDb21tdXRhdGlvbiB8fCAwDQogICAgICAgICAgdGhpcy51cmdlQWxsTW9uZXkgPSBOdW1iZXIoDQogICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLmJ0b3RhbE1vbmV5ICsNCiAgICAgICAgICAgICAgdGhpcy51cmdlZm9ybS5kdG90YWxNb25leSArDQogICAgICAgICAgICAgIHRoaXMudXJnZWZvcm0ubGlxdWlkYXRlZERhbWFnZXMgKw0KICAgICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLm90aGVyRGVidCArDQogICAgICAgICAgICAgIHRoaXMudXJnZWZvcm0ub25lQ29tbXV0YXRpb24NCiAgICAgICAgICApLnRvRml4ZWQoMikNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyB0aGlzLmRlcmF0ZWZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmJ0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS5idG90YWxNb25leSB8fCAwDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmR0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS5kdG90YWxNb25leSB8fCAwDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS5saXF1aWRhdGVkRGFtYWdlcyB8fCAwDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLm9uZUNvbW11dGF0aW9uID0gcmVzcG9uc2UuZGF0YS5vbmVDb21tdXRhdGlvbiB8fCAwDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3JtYnRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLmJ0b3RhbE1vbmV5IHx8IDANCiAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm1kdG90YWxNb25leSA9IHJlc3BvbnNlLmRhdGEuZHRvdGFsTW9uZXkgfHwgMA0KICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS5saXF1aWRhdGVkRGFtYWdlcyB8fCAwDQogICAgICAgICAgdGhpcy5kZXJhdGVmb3Jtb25lQ29tbXV0YXRpb24gPSByZXNwb25zZS5kYXRhLm9uZUNvbW11dGF0aW9uIHx8IDANCiAgICAgICAgICB0aGlzLmRlcmF0ZUFsbE1vbmV5ID0gTnVtYmVyKA0KICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmJ0b3RhbE1vbmV5ICsNCiAgICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmR0b3RhbE1vbmV5ICsNCiAgICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLmxpcXVpZGF0ZWREYW1hZ2VzICsNCiAgICAgICAgICAgICAgdGhpcy5kZXJhdGVmb3JtLm90aGVyRGVidCArDQogICAgICAgICAgICAgIHRoaXMuZGVyYXRlZm9ybS5vbmVDb21tdXRhdGlvbg0KICAgICAgICAgICkudG9GaXhlZCgyKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgdXJnZUJhY2tTZXR0bGUocm93KSB7DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgbG9hbklkOiByb3cubG9hbklkLA0KICAgICAgICBzdGF0dXM6IDEsDQogICAgICAgIHBhZ2VTaXplOiAxLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgfQ0KICAgICAgdGhpcy5sb2FuSWQgPSByb3cubG9hbklkDQogICAgICB0cmlhbF9iYWxhbmNlX29yZGVyKGRhdGEpDQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgICAgdGhpcy51cmdlZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICAgIGlmICghcmVzcG9uc2UuZGF0YS5jdXN0b21lck5hbWUpIHsNCiAgICAgICAgICAgICAgdGhpcy51cmdlZm9ybS5jdXN0b21lck5hbWUgPSByb3cuY3VzdG9tZXJOYW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEub3JnTmFtZSkgew0KICAgICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLm9yZ05hbWUgPSByb3cuamdOYW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEuYmFuaykgew0KICAgICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLmJhbmsgPSByb3cub3JnTmFtZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKCFyZXNwb25zZS5kYXRhLmxvYW5BbW91bnQpIHsNCiAgICAgICAgICAgICAgdGhpcy51cmdlZm9ybS5sb2FuQW1vdW50ID0gcm93LmNvbnRyYWN0QW10DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLmJ0b3RhbE1vbmV5ID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5idG90YWxNb25leSA6IDANCiAgICAgICAgICAgIHRoaXMudXJnZWZvcm0uZHRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgICAgdGhpcy51cmdlZm9ybS5saXF1aWRhdGVkRGFtYWdlcyA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UubGlxdWlkYXRlZERhbWFnZXMgOiAwDQogICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtLm9uZUNvbW11dGF0aW9uID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5vbmVDb21tdXRhdGlvbiA6IDANCiAgICAgICAgICAgIHRoaXMudXJnZWZvcm1idG90YWxNb25leSA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UuYnRvdGFsTW9uZXkgOiAwDQogICAgICAgICAgICB0aGlzLnVyZ2Vmb3JtZHRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgICAgdGhpcy51cmdlZm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5saXF1aWRhdGVkRGFtYWdlcyA6IDANCiAgICAgICAgICAgIHRoaXMudXJnZWZvcm1vbmVDb21tdXRhdGlvbiA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2Uub25lQ29tbXV0YXRpb24gOiAwDQogICAgICAgICAgICB0aGlzLnVyZ2VBbGxNb25leSA9IE51bWJlcigNCiAgICAgICAgICAgICAgdGhpcy51cmdlZm9ybS5idG90YWxNb25leSArIHRoaXMudXJnZWZvcm0uZHRvdGFsTW9uZXkgKyB0aGlzLnVyZ2Vmb3JtLmxpcXVpZGF0ZWREYW1hZ2VzICsgdGhpcy51cmdlZm9ybS5vbmVDb21tdXRhdGlvbg0KICAgICAgICAgICAgKS50b0ZpeGVkKDIpDQogICAgICAgICAgICB0aGlzLmFkZElkID0gcmVzcG9uc2UuZGF0YS5pZA0KICAgICAgICAgICAgdGhpcy5jaEZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMucG9zdFRyaWFsKHJvdywgMSkNCiAgICAgICAgICAgIC8vIHRoaXMudXJnZWZvcm0gPSB7fQ0KICAgICAgICAgICAgLy8gdGhpcy51cmdlZm9ybS5jdXN0b21lck5hbWUgPSByb3cuY3VzdG9tZXJOYW1lDQogICAgICAgICAgICAvLyB0aGlzLnVyZ2Vmb3JtLm9yZ05hbWUgPSByb3cuamdOYW1lDQogICAgICAgICAgICAvLyB0aGlzLnVyZ2Vmb3JtLmJhbmsgPSByb3cub3JnTmFtZQ0KICAgICAgICAgICAgLy8gdGhpcy51cmdlZm9ybS5sb2FuQW1vdW50ID0gcm93LmNvbnRyYWN0QW10DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMudXJnZUJhY2tvcGVuID0gdHJ1ZQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goZXJyb3IgPT4ge30pDQogICAgfSwNCiAgICBwb3N0VHJpYWwocm93LCBlKSB7DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgYXBwbHlJZDogcm93LmFwcGx5SWQsDQogICAgICAgIGxvYW5JZDogcm93LmxvYW5JZCwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiByb3cuY3VzdG9tZXJOYW1lLA0KICAgICAgICBvcmdOYW1lOiByb3cuamdOYW1lLA0KICAgICAgICBsb2FuQW1vdW50OiByb3cuY29udHJhY3RBbXQsDQogICAgICAgIGJhbms6IHJvdy5vcmdOYW1lLA0KICAgICAgICBwYXJ0bmVySWQ6IHJvdy5wYXJ0bmVySWQsDQogICAgICAgIHN0YXR1czogZSwNCiAgICAgICAgc2FsZXNtYW46IHJvdy5uaWNrTmFtZSwNCiAgICAgICAgb3ZlcmR1ZURheXM6IHJvdy5ib3ZlcmR1ZURheXMsDQogICAgICAgIGV4YW1pbmVTdGF0dXM6IDAsDQogICAgICB9DQogICAgICB0cmlhbF9iYWxhbmNlX3Bvc3QoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYWRkSWQgPSByZXNwb25zZS5pZA0KICAgICAgICBpZiAoZSA9PSAxKSB7DQogICAgICAgICAgdGhpcy51cmdlZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB0aGlzLmNoRm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmRlcmF0ZWZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgdGhpcy5qbUZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBjYW5jZWxMb2coKSB7DQogICAgICB0aGlzLmxvZ29wZW4gPSBmYWxzZQ0KICAgIH0sDQogICAgbG9nVmlldyhyb3cpIHsNCiAgICAgIHRoaXMubG9nTG9hbklkID0gcm93LmxvYW5JZA0KICAgICAgdGhpcy4kcmVmcy5sb2FuUmVtaW5kZXJMb2cub3BlbkxvZ0RpYWxvZygpDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXRSZW1pbmRlcihyb3cpIHsNCiAgICAgIHRoaXMuc3VibWl0TG9hbklkID0gcm93LmxvYW5JZA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmxvYW5SZW1pbmRlckxvZ1N1Ym1pdC5vcGVuRGlhbG9nKCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBsb2dWaWV3MigpIHsNCiAgICAgIGxvYW5fcmVtaW5kZXJfb3JkZXIodGhpcy5sb2dGb3JtKQ0KICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5sb2dMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICAgIHRoaXMubG9ndG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICAgIHRoaXMubG9nb3BlbiA9IHRydWUNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHt9KQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDaGFuZ2UodmFsdWUpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JnSWQgPSB2YWx1ZQ0KICAgIH0sDQogICAgLyoqIOafpeivolZJRVfliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy4kc3RvcmUuc3RhdGUudXNlcikNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RWd19hY2NvdW50X2xvYW4odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudndfYWNjb3VudF9sb2FuTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgY3VzdG9tZXJOYW1lOiBudWxsLA0KICAgICAgICBqZ05hbWU6IG51bGwsDQogICAgICAgIG9yZ05hbWU6IG51bGwsDQogICAgICAgIGJvdmVyZHVlQW1vdW50OiBudWxsLA0KICAgICAgICBkb3ZlcmR1ZUFtb3VudDogbnVsbCwNCiAgICAgIH0NCiAgICAgIHRoaXMucmVzZXRGb3JtKCdmb3JtJykNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmFsbG9jYXRpb25UaW1lKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRUaW1lID0gdGhpcy5xdWVyeVBhcmFtcy5hbGxvY2F0aW9uVGltZVswXQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFsbG9jYXRpb25UaW1lWzFdDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmN1c3RvbWVyTmFtZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2VydElkID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wbGF0ZU5vID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJ0bmVySWQgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmpnTmFtZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2xpcHBhZ2VTdGF0dXMgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZvbGxvd1N0YXR1cyA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZm9sbG93VXAgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFsbG9jYXRpb25UaW1lID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzRXh0ZW5zaW9uID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jYXJTdGF0dXMgPSAnJw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0ZpbmRDYXIgPSAnJw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAn5re75YqgVklFVycNCiAgICB9LA0KDQogICAgY2FsY01vbmV5KHByb3BvcnRpb25LZXksIG1vbmV5S2V5LCB2YWwpIHsNCiAgICAgIC8vIOWPquWBmumHkemineiuoeeul+WSjOS/neeVmeS4pOS9jeWwj+aVsA0KICAgICAgdGhpcy50cmlhbEZvcm1bbW9uZXlLZXldID0gKCh2YWwgKiB0aGlzLnRyaWFsRm9ybWFsbCkgLyAxMDApLnRvRml4ZWQoMikNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgY29uc3Qga2V5cyA9IFsnZnhqUHJvcG9ydGlvbicsICdxZFByb3BvcnRpb24nLCAnZ21qUHJvcG9ydGlvbicsICdrampQcm9wb3J0aW9uJywgJ2tqY3pQcm9wb3J0aW9uJywgJ3NiY3pQcm9wb3J0aW9uJ10NCiAgICAgIC8vIOiuoeeul+avlOS+i+S5i+WSjA0KICAgICAgY29uc3QgdG90YWwgPSBrZXlzLnJlZHVjZSgoc3VtLCBrZXkpID0+IHN1bSArIE51bWJlcih0aGlzLnRyaWFsRm9ybVtrZXldIHx8IDApLCAwKQ0KICAgICAgaWYgKHRvdGFsICE9PSAxMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5YWt6aG55q+U5L6L5LmL5ZKM5b+F6aG7562J5LqOMTAw77yBJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOmHkemineWSjOi0puWPt+exu+Wei+Wtl+auteaYoOWwhA0KICAgICAgY29uc3QgbW9uZXlBY2NvdW50TWFwID0gWw0KICAgICAgICB7IG1vbmV5OiAnZnhqTW9uZXknLCBhY2NvdW50OiAnZnhqQWNjb3VudCcsIGxhYmVsOiAn6aOO6Zmp6YeR6LSm5Y+357G75Z6LJyB9LA0KICAgICAgICB7IG1vbmV5OiAncWRNb25leScsIGFjY291bnQ6ICdxZEFjY291bnQnLCBsYWJlbDogJ+a4oOmBk+i0puWPt+exu+WeiycgfSwNCiAgICAgICAgeyBtb25leTogJ2dtak1vbmV5JywgYWNjb3VudDogJ2dtakFjY291bnQnLCBsYWJlbDogJ+W5v+aYjuWAn+i0puWPt+exu+WeiycgfSwNCiAgICAgICAgeyBtb25leTogJ2tqak1vbmV5JywgYWNjb3VudDogJ2tqakFjY291bnQnLCBsYWJlbDogJ+enkeaKgOWAn+i0puWPt+exu+WeiycgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1vbmV5OiAna2pjek1vbmV5JywNCiAgICAgICAgICBhY2NvdW50OiAna2pjekFjY291bnQnLA0KICAgICAgICAgIGxhYmVsOiAn56eR5oqA5Ye66LWE6LSm5Y+357G75Z6LJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1vbmV5OiAnc2Jjek1vbmV5JywNCiAgICAgICAgICBhY2NvdW50OiAnc2JjekFjY291bnQnLA0KICAgICAgICAgIGxhYmVsOiAn5a6I6YKm5Ye66LWE6LSm5Y+357G75Z6LJywNCiAgICAgICAgfSwNCiAgICAgIF0NCg0KICAgICAgLy8g5qCh6aqM5q+P5Liq6YeR6aKdPjDml7botKblj7fnsbvlnovlv4XloasNCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBtb25leUFjY291bnRNYXApIHsNCiAgICAgICAgaWYgKE51bWJlcih0aGlzLnRyaWFsRm9ybVtpdGVtLm1vbmV5XSkgPiAwICYmICF0aGlzLnRyaWFsRm9ybVtpdGVtLmFjY291bnRdKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDor7flhYjpgInmi6kke2l0ZW0ubGFiZWx9YCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdmFyIGRhdGEgPSB7DQogICAgICAgIGlkOiB0aGlzLnRyaWFsRm9ybS5pZCwNCiAgICAgICAgZnhqUHJvcG9ydGlvbjogdGhpcy50cmlhbEZvcm0uZnhqUHJvcG9ydGlvbiwNCiAgICAgICAgZnhqTW9uZXk6IHRoaXMudHJpYWxGb3JtLmZ4ak1vbmV5LA0KICAgICAgICBmeGpBY2NvdW50OiB0aGlzLnRyaWFsRm9ybS5meGpBY2NvdW50LA0KICAgICAgICBxZFByb3BvcnRpb246IHRoaXMudHJpYWxGb3JtLnFkUHJvcG9ydGlvbiwNCiAgICAgICAgcWRNb25leTogdGhpcy50cmlhbEZvcm0ucWRNb25leSwNCiAgICAgICAgcWRBY2NvdW50OiB0aGlzLnRyaWFsRm9ybS5xZEFjY291bnQsDQogICAgICAgIGdtalByb3BvcnRpb246IHRoaXMudHJpYWxGb3JtLmdtalByb3BvcnRpb24sDQogICAgICAgIGdtak1vbmV5OiB0aGlzLnRyaWFsRm9ybS5nbWpNb25leSwNCiAgICAgICAgZ21qQWNjb3VudDogdGhpcy50cmlhbEZvcm0uZ21qQWNjb3VudCwNCiAgICAgICAga2pqUHJvcG9ydGlvbjogdGhpcy50cmlhbEZvcm0ua2pqUHJvcG9ydGlvbiwNCiAgICAgICAga2pqTW9uZXk6IHRoaXMudHJpYWxGb3JtLmtqak1vbmV5LA0KICAgICAgICBrampBY2NvdW50OiB0aGlzLnRyaWFsRm9ybS5rampBY2NvdW50LA0KICAgICAgICBramN6UHJvcG9ydGlvbjogdGhpcy50cmlhbEZvcm0ua2pjelByb3BvcnRpb24sDQogICAgICAgIGtqY3pNb25leTogdGhpcy50cmlhbEZvcm0ua2pjek1vbmV5LA0KICAgICAgICBramN6QWNjb3VudDogdGhpcy50cmlhbEZvcm0ua2pjekFjY291bnQsDQogICAgICAgIHNiY3pQcm9wb3J0aW9uOiB0aGlzLnRyaWFsRm9ybS5zYmN6UHJvcG9ydGlvbiwNCiAgICAgICAgc2Jjek1vbmV5OiB0aGlzLnRyaWFsRm9ybS5zYmN6TW9uZXksDQogICAgICAgIHNiY3pBY2NvdW50OiB0aGlzLnRyaWFsRm9ybS5zYmN6QWNjb3VudCwNCiAgICAgICAgb3RoZXJEZWJ0OiB0aGlzLnRyaWFsRm9ybW90aGVyRGVidCwNCiAgICAgICAgdG90YWxNb25leTogdGhpcy50cmlhbEZvcm10b3RhbCwNCiAgICAgICAgaW1hZ2U6IHRoaXMudHJpYWxGb3JtLmltYWdlLm1hcChpdGVtID0+IGl0ZW0ucmVzcG9uc2UpLmpvaW4oJywnKSwNCiAgICAgICAgZXhhbWluZVN0YXR1czogMSwNCiAgICAgICAgcmVwYXltZW50VHlwZTogMCwNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKGRhdGEpDQogICAgICBzdWJfVHJpYWxfb3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+aPkOS6pOaIkOWKnycpDQogICAgICAgICAgdGhpcy5jb21tdXRlb3BlbiA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6ZmkVklFV+e8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbFZ3X2FjY291bnRfbG9hbihpZHMpDQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAndndfYWNjb3VudF9sb2FuL3Z3X2FjY291bnRfbG9hbi9leHBvcnQnLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYHZ3X2FjY291bnRfbG9hbl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKQ0KICAgIH0sDQogICAgc2VsZWN0Um93KCkgew0KICAgICAgLy8gSW1wbGVtZW50IHRoZSBsb2dpYyBmb3Igc2VsZWN0aW5nIGEgcm93DQogICAgICBjb25zb2xlLmxvZygnUm93IHNlbGVjdGVkOicsIHRoaXMudndfYWNjb3VudF9sb2FuTGlzdCkNCiAgICB9LA0KDQogICAgLy8g6YCa55So55qE5LiK5Lyg5oiQ5Yqf5aSE55CG5Ye95pWwDQogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0LCBmb3JtRmllbGQpIHsNCiAgICAgIGNvbnN0IFtvYmosIHByb3BdID0gZm9ybUZpZWxkLnNwbGl0KCcuJykNCiAgICAgIHRoaXNbb2JqXVtwcm9wXSA9IGZpbGVMaXN0DQogICAgfSwNCiAgICAvLyDpgJrnlKjnmoTliKDpmaTlpITnkIblh73mlbANCiAgICBoYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QsIGZvcm1GaWVsZCkgew0KICAgICAgY29uc3QgW29iaiwgcHJvcF0gPSBmb3JtRmllbGQuc3BsaXQoJy4nKQ0KICAgICAgdGhpc1tvYmpdW3Byb3BdID0gZmlsZUxpc3QNCiAgICB9LA0KICAgIC8vIOS4iuS8oOWksei0pQ0KICAgIGhhbmRsZVVwbG9hZEVycm9yKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+S4iuS8oOWbvueJh+Wksei0pe+8jOivt+mHjeivlScpDQogICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgIH0sDQogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHRoaXMuZGlhbG9nSW1hZ2VVcmwgPSBmaWxlLnVybA0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogIH0sDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg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file": "index.vue", "sourceRoot": "src/views/vw_account_loan/vw_account_loan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"partnerId\">\r\n        <el-select v-model=\"queryParams.partnerId\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in bankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"followUp\">\r\n        <el-input v-model=\"queryParams.followUp\" placeholder=\"跟催员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"slippageStatus\">\r\n          <el-select v-model=\"queryParams.slippageStatus\" placeholder=\"逾期状态\" clearable>\r\n            <el-option v-for=\"dict in slippageList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"isExtension\">\r\n          <el-select v-model=\"queryParams.isExtension\" placeholder=\"是否延期\" clearable>\r\n            <el-option v-for=\"dict in isExtensionList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"followStatus\">\r\n          <el-select v-model=\"queryParams.followStatus\" placeholder=\"跟催类型\" clearable>\r\n            <el-option v-for=\"dict in followUpList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"扣款时间\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.allocationTime\"\r\n            type=\"daterange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"></el-date-picker>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item label=\"\" prop=\"carStatus\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"isFindCar\">\r\n        <el-select v-model=\"queryParams.isFindCar\" placeholder=\"是否派单找车\" clearable>\r\n          <el-option v-for=\"dict in isFindCarList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item style=\"float: right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_account_loanList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"逾期状态\" align=\"center\" prop=\"slippageStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.slippageStatus != null\">\r\n            {{\r\n              scope.row.slippageStatus == 1\r\n                ? '提醒'\r\n                : scope.row.slippageStatus == 2\r\n                  ? '电催'\r\n                  : scope.row.slippageStatus == 3\r\n                    ? '上访'\r\n                    : scope.row.slippageStatus == 4\r\n                      ? '逾期30-60'\r\n                      : '逾期60+'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"还款状态\" align=\"center\" prop=\"repaymentStatus\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.repaymentStatus != null\">\r\n            {{\r\n              scope.row.repaymentStatus == 1\r\n                ? '还款中'\r\n                : scope.row.repaymentStatus == 2\r\n                  ? '已完结'\r\n                  : scope.row.repaymentStatus == 3\r\n                    ? '提前结清'\r\n                    : scope.row.repaymentStatus == 4\r\n                      ? '逾期催回结清'\r\n                      : scope.row.repaymentStatus == 5\r\n                        ? '逾期减免结清'\r\n                        : scope.row.repaymentStatus == 6\r\n                          ? '逾期未还款'\r\n                          : scope.row.repaymentStatus == 7\r\n                            ? '逾期还款中'\r\n                            : scope.row.repaymentStatus == 8\r\n                              ? '代偿未还款'\r\n                              : scope.row.repaymentStatus == 9\r\n                                ? '代偿还款中'\r\n                                : scope.row.repaymentStatus == 10\r\n                                  ? '代偿减免结清'\r\n                                  : scope.row.repaymentStatus == 11\r\n                                    ? '代偿全额结清'\r\n                                    : '未知状态'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"延期状态\" align=\"center\" prop=\"isExtension\">\r\n        <template slot-scope=\"scope\">\r\n          <span style=\"color: red\" v-if=\"scope.row.isExtension == 1\">延期</span>\r\n          <span v-else-if=\"scope.row.isExtension == 0\">未延期</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"跟催员\" align=\"center\" prop=\"followUp\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followUp\">{{ scope.row.followUp }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <span\r\n            v-if=\"scope.row.customerName\"\r\n            style=\"color: #46a6ff; cursor: pointer\"\r\n            @click=\"checkLender({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"贷款人身份证\" align=\"center\" prop=\"certId\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.certId\">{{ scope.row.certId }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.jgName\">{{ scope.row.jgName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"业务员\" align=\"center\" prop=\"nickName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.nickName\">{{ scope.row.nickName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"plateNo\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button v-if=\"scope.row.plateNo\" type=\"text\" @click=\"checkCar(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"carStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carStatus != null\">\r\n            {{\r\n              scope.row.carStatus == '1'\r\n                ? '省内正常行驶'\r\n                : scope.row.carStatus == '2'\r\n                  ? '省外正常行驶'\r\n                  : scope.row.carStatus == '3'\r\n                    ? '抵押'\r\n                    : scope.row.carStatus == '4'\r\n                      ? '疑似抵押'\r\n                      : scope.row.carStatus == '5'\r\n                        ? '疑似黑车'\r\n                        : scope.row.carStatus == '6'\r\n                          ? '已入库'\r\n                          : scope.row.carStatus == '7'\r\n                            ? '车在法院'\r\n                            : scope.row.carStatus == '8'\r\n                              ? '已法拍'\r\n                              : '协商卖车'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车辆位置\" align=\"center\" prop=\"carDetailAddress\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carDetailAddress\">{{ scope.row.carDetailAddress }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"GPS状态\" align=\"center\" prop=\"gpsStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.gpsStatus != null\">\r\n            {{\r\n              scope.row.gpsStatus == '1'\r\n                ? '部分拆除'\r\n                : scope.row.gpsStatus == '2'\r\n                  ? '全部拆除'\r\n                  : scope.row.gpsStatus == '3'\r\n                    ? 'GPS正常'\r\n                    : '停车30天以上'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"派车团队\" align=\"center\" prop=\"carTeamName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carTeamName\">{{ scope.row.carTeamName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"bank\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.bank\">{{ scope.row.bank }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n\r\n\r\n      <el-table-column label=\"银行逾期天数\" align=\"center\" prop=\"boverdueDays\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.boverdueDays != null\">{{ scope.row.boverdueDays }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"首期逾期金额\" align=\"center\" prop=\"foverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.foverdueAmount != null\">{{ scope.row.foverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"银行逾期金额\" align=\"center\" prop=\"boverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.boverdueAmount != null\">{{ scope.row.boverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"代扣逾期天数\" align=\"center\" prop=\"doverdueDays\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.doverdueDays != null\">{{ scope.row.doverdueDays }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"代扣逾期金额\" align=\"center\" prop=\"doverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.doverdueAmount != null\">{{ scope.row.doverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催回金额\" align=\"center\" prop=\"realReturnMoney\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.realReturnMoney != null\" style=\"color: #46a6ff; cursor: pointer\" @click=\"checkReminder(scope.row.loanId)\">\r\n            {{ scope.row.realReturnMoney }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催记类型\" align=\"center\" prop=\"followStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followStatus != null\">\r\n            {{ scope.row.followStatus == 1 ? '继续联系' : scope.row.followStatus == 2 ? '约定还款' : '无法跟进' }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催记提交日期\" align=\"center\" prop=\"followDate\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followDate\">{{ parseTime(scope.row.followDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"下次跟进时间\" align=\"center\" prop=\"trackingTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.trackingTime\">\r\n            {{ parseTime(scope.row.trackingTime, '{y}-{m}-{d}') }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"预计还款日期\" align=\"center\" prop=\"appointedTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.appointedTime\">\r\n            {{ parseTime(scope.row.appointedTime, '{y}-{m}-{d}') }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催回日期\" align=\"center\" prop=\"reminderDate\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.reminderDate\">{{ parseTime(scope.row.reminderDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleSubmitReminder(scope.row)\"\r\n                v-hasPermi=\"['loan_reminder:loan_reminder:add']\">\r\n                提交催记\r\n              </el-button>\r\n              <el-button class=\"operation-btn\" size=\"mini\" type=\"text\" @click=\"logView(scope.row)\">催记日志</el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"initiate(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:initiate']\">\r\n                发起代偿\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"urgeBackSettle(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:collect']\">\r\n                催回结清\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"derateSettle(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:derate']\">\r\n                减免结清\r\n              </el-button>\r\n              <el-button class=\"operation-btn\" size=\"mini\" type=\"text\" @click=\"planBth(scope.row)\">还款计划</el-button>\r\n              <el-popconfirm title=\"确认预估呆账吗？\" @confirm=\"handleEstimateBadDebt(scope.row)\">\r\n                <el-button slot=\"reference\" class=\"operation-btn\" size=\"mini\" type=\"text\">预估呆账</el-button>\r\n              </el-popconfirm>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"催记列表\" :visible.sync=\"reminderShow\" width=\"700px\" append-to-body>\r\n      <el-table :data=\"reminderList\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"identity\" label=\"身份\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span>\r\n              {{\r\n                scope.row.identity == 1\r\n                  ? '业务员'\r\n                  : scope.row.identity == 2\r\n                    ? '贷后文员'\r\n                    : scope.row.identity == 3\r\n                      ? '电催员'\r\n                      : scope.row.identity == 4\r\n                        ? '上访员'\r\n                        : scope.row.identity == 5\r\n                          ? '强制上访员'\r\n                          : scope.row.identity == 6\r\n                            ? '找车员'\r\n                            : '法诉文员'\r\n              }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"customerName\" label=\"催回人\" width=\"120\"></el-table-column>\r\n        <el-table-column prop=\"bmoney\" label=\"银行金额\"></el-table-column>\r\n        <el-table-column prop=\"dmoney\" label=\"代扣金额\"></el-table-column>\r\n        <el-table-column label=\"总金额\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bmoney + scope.row.dmoney }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"trackingTime\" label=\"催回日期\" width=\"130\"></el-table-column>\r\n        <el-table-column label=\"操作\">\r\n          <template>\r\n            <span style=\"color: #46a6ff; cursor: pointer\">详情</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"发起代偿\" :visible.sync=\"commuteopen\" width=\"900px\" append-to-body>\r\n      <div class=\"settle_money\" @click=\"trialSub\">发起试算</div>\r\n      <el-form ref=\"trialForm\" :model=\"trialForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"trialForm.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"trialForm.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"trialForm.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"trialForm.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"剩余本金\">\r\n              <el-input v-model=\"trialFormprincipal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行逾期金额\">\r\n              <el-input v-model=\"trialFormboverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行利息\">\r\n              <el-input v-model=\"trialForminterest\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代偿总金额\">\r\n              <el-input v-model=\"trialFormall\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划比例\" prop=\"fxjProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.fxjProportion\"\r\n                @input=\"onInputLimit('fxjProportion', $event)\"\r\n                @change=\"calcMoney('fxjProportion', 'fxjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划金额\" prop=\"fxjMoney\">\r\n              <el-input v-model=\"trialForm.fxjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"fxjAccount\">\r\n              <el-select v-model=\"trialForm.fxjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入比例\" prop=\"qdProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.qdProportion\"\r\n                @input=\"onInputLimit('qdProportion', $event)\"\r\n                @change=\"calcMoney('qdProportion', 'qdMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入金额\" prop=\"qdMoney\">\r\n              <el-input v-model=\"trialForm.qdMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"qdAccount\">\r\n              <el-select v-model=\"trialForm.qdAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借比例\" prop=\"gmjProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.gmjProportion\"\r\n                @input=\"onInputLimit('gmjProportion', $event)\"\r\n                @change=\"calcMoney('gmjProportion', 'gmjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借额\" prop=\"gmjMoney\">\r\n              <el-input v-model=\"trialForm.gmjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"gmjAccount\">\r\n              <el-select v-model=\"trialForm.gmjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借比例\" prop=\"kjczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.kjjProportion\"\r\n                @input=\"onInputLimit('kjjProportion', $event)\"\r\n                @change=\"calcMoney('kjjProportion', 'kjjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借额\" prop=\"kjjMoney\">\r\n              <el-input v-model=\"trialForm.kjjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjjAccount\">\r\n              <el-select v-model=\"trialForm.kjjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资比例\" prop=\"kjczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.kjczProportion\"\r\n                @input=\"onInputLimit('kjczProportion', $event)\"\r\n                @change=\"calcMoney('kjczProportion', 'kjczMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资金额\" prop=\"kjczMoney\">\r\n              <el-input v-model=\"trialForm.kjczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjczAccount\">\r\n              <el-select v-model=\"trialForm.kjczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资比例\" prop=\"sbczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.sbczProportion\"\r\n                @input=\"onInputLimit('sbczProportion', $event)\"\r\n                @change=\"calcMoney('sbczProportion', 'sbczMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资金额\" prop=\"sbczMoney\">\r\n              <el-input v-model=\"trialForm.sbczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"sbczAccount\">\r\n              <el-select v-model=\"trialForm.sbczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"上传凭据\">\r\n              <div v-if=\"trialForm.examineStatus >= 1\">\r\n                <template v-if=\"trialForm.image && trialForm.image.length > 0\">\r\n                  <el-image\r\n                    v-for=\"(url, index) in trialForm.image\"\r\n                    :key=\"index\"\r\n                    style=\"width: 100px; height: 100px; margin-right: 10px\"\r\n                    :src=\"url\"\r\n                    fit=\"cover\"\r\n                    :preview-src-list=\"trialForm.image\"></el-image>\r\n                </template>\r\n                <el-upload v-else list-type=\"picture-card\" :disabled=\"true\" :action=\"uploadImgUrl\">\r\n                  <div>暂无凭据</div>\r\n                </el-upload>\r\n              </div>\r\n              <el-upload\r\n                v-else\r\n                :data=\"data\"\r\n                :action=\"uploadImgUrl\"\r\n                list-type=\"picture-card\"\r\n                :headers=\"headers\"\r\n                :file-list=\"trialForm.image\"\r\n                :on-preview=\"handlePictureCardPreview\"\r\n                :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'trialForm.image')\"\r\n                :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'trialForm.image')\"\r\n                :on-error=\"handleUploadError\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\">\r\n              <el-input v-model=\"trialFormdoverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\">\r\n              <el-input v-model=\"trialFormliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"登记其他欠款金额\" prop=\"otherDebt\">\r\n              <el-input type=\"number\" @input=\"handleInput3\" v-model=\"trialFormotherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总账款\">\r\n              <el-input v-model=\"trialFormtotal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!trialForm.examineStatus\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancelcommute\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 催回结清对话框 -->\r\n    <el-dialog\r\n      :before-close=\"cancelurgeBack\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"dialogBox\"\r\n      title=\"催回结清\"\r\n      :visible.sync=\"urgeBackopen\"\r\n      width=\"800px\"\r\n      append-to-body>\r\n      <div class=\"settle_money\" @click=\"getUrgeMoney(1)\" v-if=\"urgeform.examineStatus < 1\">获取结清金额</div>\r\n      <el-form ref=\"urgeform\" :model=\"urgeform\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"urgeform.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"urgeform.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"urgeform.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"urgeform.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行结清金额\" prop=\"btotalMoney\">\r\n              <el-input v-model=\"urgeformbtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber1\">\r\n              <el-select v-model=\"urgeform.accountNumber1\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\" prop=\"dtotalMoney\">\r\n              <el-input v-model=\"urgeformdtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber2\">\r\n              <el-select v-model=\"urgeform.accountNumber2\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\" prop=\"liquidatedDamages\">\r\n              <el-input v-model=\"urgeformliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber3\">\r\n              <el-select v-model=\"urgeform.accountNumber3\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"其他欠款\" prop=\"otherDebt\">\r\n              <el-input @input=\"handleInput1\" v-model=\"urgeform.otherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber4\">\r\n              <el-select v-model=\"urgeform.accountNumber4\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单期代偿金\" prop=\"oneCommutation\">\r\n              <el-input v-model=\"urgeformoneCommutation\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber5\">\r\n              <el-select v-model=\"urgeform.accountNumber5\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总欠款金额\">\r\n              <el-input v-model=\"urgeAllMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" v-if=\"urgeform.examineStatus <= 1\" @click=\"submitUrge\">确 定</el-button>\r\n        <el-button @click=\"cancelurgeBack\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 减免结清对话框 -->\r\n    <el-dialog\r\n      :before-close=\"cancelderate\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"dialogBox\"\r\n      title=\"减免结清\"\r\n      :visible.sync=\"derateopen\"\r\n      width=\"800px\"\r\n      append-to-body>\r\n      <div class=\"settle_money\" @click=\"getUrgeMoney(2)\" v-if=\"urgeform.examineStatus < 1\">获取结清金额</div>\r\n      <el-form ref=\"derateform\" :model=\"derateform\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"derateform.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"derateform.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"derateform.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"derateform.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行结清金额\" prop=\"btotalMoney\">\r\n              <el-input v-model=\"derateformbtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber1\">\r\n              <el-select v-model=\"derateform.accountNumber1\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\" prop=\"dtotalMoney\">\r\n              <el-input v-model=\"derateformdtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber2\">\r\n              <el-select v-model=\"derateform.accountNumber2\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\" prop=\"liquidatedDamages\">\r\n              <el-input v-model=\"derateformliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber3\">\r\n              <el-select v-model=\"derateform.accountNumber3\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"其他欠款\" prop=\"otherDebt\">\r\n              <el-input @input=\"handleInput2\" v-model=\"derateform.otherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber4\">\r\n              <el-select v-model=\"derateform.accountNumber4\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单期代偿金\" prop=\"oneCommutation\">\r\n              <el-input v-model=\"derateformoneCommutation\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber5\">\r\n              <el-select v-model=\"derateform.accountNumber5\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总欠款金额\">\r\n              <el-input v-model=\"derateAllMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" v-if=\"derateform.examineStatus <= 1\" @click=\"submitDerate\">确 定</el-button>\r\n        <el-button @click=\"cancelderate\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"lenderShow\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"1\" />\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"logLoanId\" />\r\n    <!-- 提交催记组件 -->\r\n    <loan-reminder-log-submit ref=\"loanReminderLogSubmit\" :loan-id=\"submitLoanId\" :status=\"3\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  trial_balance_put,\r\n  trial_submit_order,\r\n  trial_balance_post,\r\n  trial_balance_order,\r\n  loan_reminder_order,\r\n  listVw_account_loan,\r\n  delVw_account_loan,\r\n  loan_compensation_order,\r\n  add_Trial_order,\r\n  dc_submit_order,\r\n  sub_Trial_order,\r\n  update_loan_list,\r\n  get_bank_account,\r\n} from '@/api/vw_account_loan/vw_account_loan'\r\nimport { getToken } from '@/utils/auth'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\nimport LoanReminderLogSubmit from '@/layout/components/Dialog/loanReminderLogSubmit.vue'\r\n\r\nexport default {\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n    LoanReminderLog,\r\n    LoanReminderLogSubmit,\r\n  },\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 上传接口地址\r\n    action: {\r\n      type: String,\r\n      // default: \"/common/upload\"\r\n      default: '/common/ossupload',\r\n    },\r\n    // 上传携带的参数\r\n    data: {\r\n      type: Object,\r\n    },\r\n  },\r\n  name: 'Vw_account_loan',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 控制更多筛选条件显示\r\n      showMore: false,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vw_account_loanList: [],\r\n      // 是否显示弹出层\r\n      logopen: false, //催记日志\r\n      currentRow: {}, //dialog传入数据\r\n      urgeBackopen: false, //催回结清\r\n      derateopen: false, //减免结清\r\n      commuteopen: false, //发起代偿\r\n      detailShow: false, //查看日志详情\r\n      lenderShow: false, //贷款人信息\r\n      customerInfo: {\r\n        customerId: '',\r\n        applyId: '',\r\n      },\r\n      carShow: false, // 车辆信息\r\n      plateNo: '', // 车辆编号\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        customerName: null,\r\n        certId: null,\r\n        plateNo: null,\r\n        partnerId: null,\r\n        jgName: null,\r\n        slippageStatus: null,\r\n        followStatus: null,\r\n        followUp: null,\r\n        allocationTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        isExtension: '',\r\n        carStatus: '',\r\n        isFindCar: '',\r\n      },\r\n\r\n      bankList: [\r\n        { value: 'EO00000010', label: '苏银金租' },\r\n        { value: 'IO00000006', label: '浙商银行' },\r\n        { value: 'IO00000007', label: '中关村银行' },\r\n        { value: 'IO00000008', label: '蓝海银行' },\r\n        { value: 'IO00000009', label: '华瑞银行' },\r\n        { value: 'IO00000010', label: '皖新租赁' },\r\n      ],\r\n\r\n      isExtensionList: [\r\n        { label: '否', value: '0' },\r\n        { label: '是', value: '1' },\r\n      ],\r\n      slippageList: [\r\n        { label: '提醒', value: 1 },\r\n        { label: '电催', value: 2 },\r\n        { label: '上访', value: 3 },\r\n        { label: '逾期30-60', value: 4 },\r\n        { label: '逾期60+', value: 5 },\r\n      ],\r\n      followUpList: [\r\n        { label: '继续联系', value: 1 },\r\n        { label: '约定还款', value: 2 },\r\n        { label: '无法跟进', value: 3 },\r\n      ],\r\n      accountList: [], //银行账户列表\r\n      // 表单校验\r\n      rules: {},\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      textarea: null,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      reminderList: [], //催记列表\r\n      reminderShow: false,\r\n      urgeform: {},\r\n      derateform: {},\r\n      radio: 0,\r\n      logList: [], //催记日志\r\n      logDetail: {}, //催记日志详情\r\n      logForm: {\r\n        loanId: null,\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n      },\r\n      logtotal: 0,\r\n      urgeAllMoney: 0, //催回结清总欠款\r\n      derateAllMoney: 0, //减免结清总欠款\r\n      loanId: null,\r\n      addId: null, //催回、减免新增id\r\n      jmForm: {},\r\n      chForm: {},\r\n      urgeformbtotalMoney: 0,\r\n      urgeformdtotalMoney: 0,\r\n      urgeformliquidatedDamages: 0,\r\n      urgeformoneCommutation: 0,\r\n      derateformbtotalMoney: 0,\r\n      derateformdtotalMoney: 0,\r\n      derateformliquidatedDamages: 0,\r\n      derateformoneCommutation: 0,\r\n      trialForm: {},\r\n      trialFormprincipal: 0,\r\n      trialFormboverdueAmount: 0,\r\n      trialForminterest: 0,\r\n      trialFormall: 0,\r\n      trialFormdoverdueAmount: 0,\r\n      trialFormliquidatedDamages: 0,\r\n      trialFormtotal: 0,\r\n      trialFormotherDebt: 0,\r\n      dkyqMoney: 0,\r\n      bankyqMoney: 0,\r\n      lendingBank: null,\r\n      OrderingChannel: null,\r\n      logLoanId: null, //嵌入催记日志组件的loanId\r\n      submitLoanId: null, //嵌入提交催记组件的loanId\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      isFindCarList: [\r\n        { label: '未派单', value: '0' },\r\n        { label: '已派单', value: '1' },\r\n      ],\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getAccountList()\r\n  },\r\n  methods: {\r\n    getAccountList() {\r\n      get_bank_account().then(response => {\r\n        this.accountList = response.rows\r\n      })\r\n    },\r\n    handleEstimateBadDebt(row) {\r\n      const data = {\r\n        id: row.loanId,\r\n        badDebt: 1,\r\n      }\r\n      update_loan_list(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('预估呆账成功')\r\n        } else {\r\n          this.$modal.msgError('预估呆账失败')\r\n        }\r\n      })\r\n    },\r\n    trialSub() {\r\n      var data = {\r\n        applyId: this.trialForm.applyId,\r\n        id: this.trialForm.id,\r\n        loanId: this.trialForm.loanId,\r\n        loanAmount: this.trialForm.loanAmount,\r\n        partnerId: this.trialForm.partnerId,\r\n      }\r\n      dc_submit_order(data).then(response => {\r\n        this.trialFormprincipal = response.data.principal || 0\r\n        this.trialForm.defaultInterest = response.data.defaultInterest || 0\r\n        this.trialForminterest = response.data.interest || 0\r\n        this.trialFormall = response.data.btotalMoney || 0\r\n        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0\r\n        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0\r\n        this.trialFormtotal = Number(\r\n          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n        ).toFixed(2)\r\n      })\r\n    },\r\n    submitDerate() {\r\n      if (this.derateformbtotalMoney && !this.derateform.accountNumber1) {\r\n        this.$modal.msgError('请选择银行结清账号')\r\n        return\r\n      }\r\n      if (this.derateformdtotalMoney && !this.derateform.accountNumber2) {\r\n        this.$modal.msgError('请选择代扣剩余账号')\r\n        return\r\n      }\r\n      if (this.derateformliquidatedDamages && !this.derateform.accountNumber3) {\r\n        this.$modal.msgError('请选择违约金账号')\r\n        return\r\n      }\r\n      if (this.derateform.otherDebt && !this.derateform.accountNumber4) {\r\n        this.$modal.msgError('请选择其他欠款账号')\r\n        return\r\n      }\r\n      if (this.derateformoneCommutation && !this.derateform.accountNumber5) {\r\n        this.$modal.msgError('请选择单期代偿金账号')\r\n        return\r\n      }\r\n      var data = {\r\n        customerName: this.derateform.customerName,\r\n        orgName: this.derateform.orgName,\r\n        bank: this.derateform.bank,\r\n        loanAmount: this.derateform.loanAmount,\r\n        btotalMoney: this.derateform.btotalMoney,\r\n        accountNumber1: this.derateform.accountNumber1,\r\n        dtotalMoney: this.derateform.dtotalMoney,\r\n        accountNumber2: this.derateform.accountNumber2,\r\n        liquidatedDamages: this.derateform.liquidatedDamages,\r\n        accountNumber3: this.derateform.accountNumber3,\r\n        otherDebt: this.derateform.otherDebt,\r\n        accountNumber4: this.derateform.accountNumber4,\r\n        oneCommutation: this.derateform.oneCommutation,\r\n        accountNumber5: this.derateform.accountNumber5,\r\n        totalMoney: this.derateAllMoney,\r\n        id: this.addId,\r\n        status: 2,\r\n        examineStatus: 1,\r\n      }\r\n      trial_balance_put(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.derateopen = false\r\n          this.derateform = {}\r\n          this.derateformbtotalMoney = 0\r\n          this.derateformdtotalMoney = 0\r\n          this.derateformliquidatedDamages = 0\r\n          this.derateformoneCommutation = 0\r\n        }\r\n      })\r\n    },\r\n    submitUrge() {\r\n      if (this.urgeformbtotalMoney && !this.urgeform.accountNumber1) {\r\n        this.$modal.msgError('请选择银行结清账号')\r\n        return\r\n      }\r\n      if (this.urgeformdtotalMoney && !this.urgeform.accountNumber2) {\r\n        this.$modal.msgError('请选择代扣剩余账号')\r\n        return\r\n      }\r\n      if (this.urgeformliquidatedDamages && !this.urgeform.accountNumber3) {\r\n        this.$modal.msgError('请选择违约金账号')\r\n        return\r\n      }\r\n      if (this.urgeform.otherDebt && !this.urgeform.accountNumber4) {\r\n        this.$modal.msgError('请选择其他欠款账号')\r\n        return\r\n      }\r\n      if (this.urgeformoneCommutation && !this.urgeform.accountNumber5) {\r\n        this.$modal.msgError('请选择单期代偿金账号')\r\n        return\r\n      }\r\n      var data = {\r\n        customerName: this.urgeform.customerName,\r\n        orgName: this.urgeform.orgName,\r\n        bank: this.urgeform.bank,\r\n        loanAmount: this.urgeform.loanAmount,\r\n        btotalMoney: this.urgeform.btotalMoney,\r\n        accountNumber1: this.urgeform.accountNumber1,\r\n        dtotalMoney: this.urgeform.dtotalMoney,\r\n        accountNumber2: this.urgeform.accountNumber2,\r\n        liquidatedDamages: this.urgeform.liquidatedDamages,\r\n        accountNumber3: this.urgeform.accountNumber3,\r\n        otherDebt: this.urgeform.otherDebt,\r\n        accountNumber4: this.urgeform.accountNumber4,\r\n        oneCommutation: this.urgeform.oneCommutation,\r\n        accountNumber5: this.urgeform.accountNumber5,\r\n        totalMoney: this.urgeAllMoney,\r\n        id: this.addId,\r\n        status: 1,\r\n        examineStatus: 1,\r\n      }\r\n      trial_balance_put(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.urgeBackopen = false\r\n          this.urgeform = {}\r\n          this.urgeformbtotalMoney = 0\r\n          this.urgeformdtotalMoney = 0\r\n          this.urgeformliquidatedDamages = 0\r\n          this.urgeformoneCommutation = 0\r\n        }\r\n      })\r\n    },\r\n    onInputLimit(key, value, min = 0, max = 100) {\r\n      let val = Number(value)\r\n      if (isNaN(val)) val = min\r\n      if (val > max) val = max\r\n      if (val < min) val = min\r\n      this.trialForm[key] = Number(val)\r\n    },\r\n    cancelDetail() {\r\n      this.detailShow = false\r\n    },\r\n    //查看车牌信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    //查看贷款人信息\r\n    checkLender(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.lenderShow = true\r\n    },\r\n    planBth(row) {\r\n      var data = {\r\n        partnerId: row.partnerId,\r\n        applyId: row.applyId,\r\n      }\r\n      if (row.partnerId == 'IO00000008') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/lhindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'EO00000010') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/syindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000006') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/zsindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000007') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/zgcindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000009') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/hrindex',\r\n          query: data,\r\n        })\r\n      } else {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/wxindex',\r\n          query: data,\r\n        })\r\n      }\r\n    },\r\n    checkReminder(id) {\r\n      var data = {\r\n        loanId: id,\r\n        urgeStatus: 2,\r\n      }\r\n      loan_reminder_order(data).then(response => {\r\n        this.reminderList = response.rows\r\n        this.reminderShow = true\r\n      })\r\n    },\r\n\r\n    cancelcommute() {\r\n      this.commuteopen = false\r\n    },\r\n    initiate(row) {\r\n      //发起代偿\r\n      var data = {\r\n        loanId: row.loanId,\r\n      }\r\n      loan_compensation_order(data).then(response => {\r\n        if (response.data) {\r\n          this.trialForm = response.data\r\n          this.trialForm.loanAmount = row.contractAmt || 0\r\n          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0\r\n          this.trialFormboverdueAmount = row.boverdueAmount || 0\r\n          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0\r\n          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0\r\n          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)\r\n          this.trialFormtotal = Number(\r\n            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n          ).toFixed(2)\r\n          this.trialForm.image = response.data.image ? response.data.image.split(',') : []\r\n\r\n          this.commuteopen = true\r\n        } else {\r\n          this.addTrial(row)\r\n          this.trialForm.image = []\r\n        }\r\n      })\r\n    },\r\n    addTrial(row) {\r\n      console.log(row)\r\n      var data = {\r\n        id: row.id,\r\n        applyId: row.applyId,\r\n        loanId: row.loanId,\r\n        customerId: row.customerId,\r\n        customerName: row.customerName,\r\n        salesman: row.nickName,\r\n        orgName: row.jgName,\r\n        partnerId: row.partnerId,\r\n        bank: row.orgName,\r\n        loanAmount: row.contractAmt,\r\n        examineStatus: 0,\r\n      }\r\n      add_Trial_order(data).then(response => {\r\n        this.trialForm = response.data || {}\r\n        this.commuteopen = true\r\n      })\r\n    },\r\n    cancelderate() {\r\n      this.derateform = {}\r\n      this.jmForm = {}\r\n      this.derateformbtotalMoney = 0\r\n      this.derateformdtotalMoney = 0\r\n      this.derateformliquidatedDamages = 0\r\n      this.derateformoneCommutation = 0\r\n      this.derateAllMoney = 0\r\n      this.derateopen = false\r\n    },\r\n    handleInput3(value) {\r\n      this.trialFormotherDebt = Number(value)\r\n      this.trialFormtotal = Number(\r\n        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n      ).toFixed(2)\r\n    },\r\n    handleInput2(value) {\r\n      this.derateform.otherDebt = Number(value)\r\n      this.derateAllMoney = Number(\r\n        this.derateform.btotalMoney +\r\n          this.derateform.dtotalMoney +\r\n          this.derateform.liquidatedDamages +\r\n          this.derateform.otherDebt +\r\n          this.derateform.oneCommutation\r\n      ).toFixed(2)\r\n    },\r\n    handleInput1(value) {\r\n      this.urgeform.otherDebt = Number(value)\r\n      this.urgeAllMoney = Number(\r\n        this.urgeform.btotalMoney +\r\n          this.urgeform.dtotalMoney +\r\n          this.urgeform.liquidatedDamages +\r\n          this.urgeform.otherDebt +\r\n          this.urgeform.oneCommutation\r\n      ).toFixed(2)\r\n    },\r\n    derateSettle(row) {\r\n      var data = {\r\n        loanId: row.loanId,\r\n        status: 2,\r\n        pageSize: 1,\r\n        pageNum: 1,\r\n      }\r\n      this.loanId = row.loanId\r\n      trial_balance_order(data)\r\n        .then(response => {\r\n          if (response.data) {\r\n            this.derateform = response.data\r\n            if (!response.data.customerName) {\r\n              this.derateform.customerName = row.customerName\r\n            }\r\n            if (!response.data.orgName) {\r\n              this.derateform.orgName = row.jgName\r\n            }\r\n            if (!response.data.bank) {\r\n              this.derateform.bank = row.orgName\r\n            }\r\n            if (!response.data.loanAmount) {\r\n              this.derateform.loanAmount = row.contractAmt\r\n            }\r\n            this.derateform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.derateform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.derateform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.derateform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.derateformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.derateformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.derateformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.derateformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.derateAllMoney = Number(\r\n              this.derateform.btotalMoney + this.derateform.dtotalMoney + this.derateform.liquidatedDamages + this.derateform.oneCommutation\r\n            ).toFixed(2)\r\n            this.addId = response.data.id\r\n            this.jmForm = response.data\r\n          } else {\r\n            this.postTrial(row, 2)\r\n            // this.derateform = {}\r\n            // this.derateform.customerName = row.customerName\r\n            // this.derateform.orgName = row.jgName\r\n            // this.derateform.bank = row.orgName\r\n            // this.derateform.loanAmount = row.contractAmt\r\n          }\r\n        })\r\n        .catch(error => {})\r\n      this.derateopen = true\r\n    },\r\n    cancelurgeBack() {\r\n      this.urgeform = {}\r\n      this.chForm = {}\r\n      this.urgeformbtotalMoney = 0\r\n      this.urgeformdtotalMoney = 0\r\n      this.urgeformliquidatedDamages = 0\r\n      this.urgeformoneCommutation = 0\r\n      this.urgeAllMoney = 0\r\n      this.urgeBackopen = false\r\n    },\r\n    getUrgeMoney(e) {\r\n      var data = {}\r\n      if (e == 1) {\r\n        data = this.chForm\r\n      } else {\r\n        data = this.jmForm\r\n      }\r\n      trial_submit_order(data).then(response => {\r\n        if (e == 1) {\r\n          // this.urgeform = response.data\r\n          this.urgeform.btotalMoney = response.data.btotalMoney || 0\r\n          this.urgeform.dtotalMoney = response.data.dtotalMoney || 0\r\n          this.urgeform.liquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.urgeform.oneCommutation = response.data.oneCommutation || 0\r\n          this.urgeformbtotalMoney = response.data.btotalMoney || 0\r\n          this.urgeformdtotalMoney = response.data.dtotalMoney || 0\r\n          this.urgeformliquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.urgeformoneCommutation = response.data.oneCommutation || 0\r\n          this.urgeAllMoney = Number(\r\n            this.urgeform.btotalMoney +\r\n              this.urgeform.dtotalMoney +\r\n              this.urgeform.liquidatedDamages +\r\n              this.urgeform.otherDebt +\r\n              this.urgeform.oneCommutation\r\n          ).toFixed(2)\r\n        } else {\r\n          // this.derateform = response.data\r\n          this.derateform.btotalMoney = response.data.btotalMoney || 0\r\n          this.derateform.dtotalMoney = response.data.dtotalMoney || 0\r\n          this.derateform.liquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.derateform.oneCommutation = response.data.oneCommutation || 0\r\n          this.derateformbtotalMoney = response.data.btotalMoney || 0\r\n          this.derateformdtotalMoney = response.data.dtotalMoney || 0\r\n          this.derateformliquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.derateformoneCommutation = response.data.oneCommutation || 0\r\n          this.derateAllMoney = Number(\r\n            this.derateform.btotalMoney +\r\n              this.derateform.dtotalMoney +\r\n              this.derateform.liquidatedDamages +\r\n              this.derateform.otherDebt +\r\n              this.derateform.oneCommutation\r\n          ).toFixed(2)\r\n        }\r\n      })\r\n    },\r\n    urgeBackSettle(row) {\r\n      var data = {\r\n        loanId: row.loanId,\r\n        status: 1,\r\n        pageSize: 1,\r\n        pageNum: 1,\r\n      }\r\n      this.loanId = row.loanId\r\n      trial_balance_order(data)\r\n        .then(response => {\r\n          if (response.data) {\r\n            this.urgeform = response.data\r\n            if (!response.data.customerName) {\r\n              this.urgeform.customerName = row.customerName\r\n            }\r\n            if (!response.data.orgName) {\r\n              this.urgeform.orgName = row.jgName\r\n            }\r\n            if (!response.data.bank) {\r\n              this.urgeform.bank = row.orgName\r\n            }\r\n            if (!response.data.loanAmount) {\r\n              this.urgeform.loanAmount = row.contractAmt\r\n            }\r\n            this.urgeform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.urgeform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.urgeform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.urgeform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.urgeformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.urgeformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.urgeformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.urgeformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.urgeAllMoney = Number(\r\n              this.urgeform.btotalMoney + this.urgeform.dtotalMoney + this.urgeform.liquidatedDamages + this.urgeform.oneCommutation\r\n            ).toFixed(2)\r\n            this.addId = response.data.id\r\n            this.chForm = response.data\r\n          } else {\r\n            this.postTrial(row, 1)\r\n            // this.urgeform = {}\r\n            // this.urgeform.customerName = row.customerName\r\n            // this.urgeform.orgName = row.jgName\r\n            // this.urgeform.bank = row.orgName\r\n            // this.urgeform.loanAmount = row.contractAmt\r\n          }\r\n          this.urgeBackopen = true\r\n        })\r\n        .catch(error => {})\r\n    },\r\n    postTrial(row, e) {\r\n      var data = {\r\n        applyId: row.applyId,\r\n        loanId: row.loanId,\r\n        customerName: row.customerName,\r\n        orgName: row.jgName,\r\n        loanAmount: row.contractAmt,\r\n        bank: row.orgName,\r\n        partnerId: row.partnerId,\r\n        status: e,\r\n        salesman: row.nickName,\r\n        overdueDays: row.boverdueDays,\r\n        examineStatus: 0,\r\n      }\r\n      trial_balance_post(data).then(response => {\r\n        this.addId = response.id\r\n        if (e == 1) {\r\n          this.urgeform = response.data\r\n          this.chForm = response.data\r\n        } else {\r\n          this.derateform = response.data\r\n          this.jmForm = response.data\r\n        }\r\n      })\r\n    },\r\n    cancelLog() {\r\n      this.logopen = false\r\n    },\r\n    logView(row) {\r\n      this.logLoanId = row.loanId\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleSubmitReminder(row) {\r\n      this.submitLoanId = row.loanId\r\n      this.$nextTick(() => {\r\n        this.$refs.loanReminderLogSubmit.openDialog()\r\n      })\r\n    },\r\n    logView2() {\r\n      loan_reminder_order(this.logForm)\r\n        .then(response => {\r\n          this.logList = response.rows\r\n          this.logtotal = response.total\r\n          this.logopen = true\r\n        })\r\n        .catch(error => {})\r\n    },\r\n\r\n    handleChange(value) {\r\n      this.queryParams.orgId = value\r\n    },\r\n    /** 查询VIEW列表 */\r\n    getList() {\r\n      console.log(this.$store.state.user)\r\n      this.loading = true\r\n      listVw_account_loan(this.queryParams).then(response => {\r\n        this.vw_account_loanList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        customerName: null,\r\n        jgName: null,\r\n        orgName: null,\r\n        boverdueAmount: null,\r\n        doverdueAmount: null,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.allocationTime) {\r\n        this.queryParams.startTime = this.queryParams.allocationTime[0]\r\n        this.queryParams.endTime = this.queryParams.allocationTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.certId = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.partnerId = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.slippageStatus = null\r\n      this.queryParams.followStatus = null\r\n      this.queryParams.followUp = null\r\n      this.queryParams.allocationTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.isExtension = null\r\n      this.queryParams.carStatus = ''\r\n      this.queryParams.isFindCar = ''\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加VIEW'\r\n    },\r\n\r\n    calcMoney(proportionKey, moneyKey, val) {\r\n      // 只做金额计算和保留两位小数\r\n      this.trialForm[moneyKey] = ((val * this.trialFormall) / 100).toFixed(2)\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const keys = ['fxjProportion', 'qdProportion', 'gmjProportion', 'kjjProportion', 'kjczProportion', 'sbczProportion']\r\n      // 计算比例之和\r\n      const total = keys.reduce((sum, key) => sum + Number(this.trialForm[key] || 0), 0)\r\n      if (total !== 100) {\r\n        this.$message.error('六项比例之和必须等于100！')\r\n        return\r\n      }\r\n\r\n      // 金额和账号类型字段映射\r\n      const moneyAccountMap = [\r\n        { money: 'fxjMoney', account: 'fxjAccount', label: '风险金账号类型' },\r\n        { money: 'qdMoney', account: 'qdAccount', label: '渠道账号类型' },\r\n        { money: 'gmjMoney', account: 'gmjAccount', label: '广明借账号类型' },\r\n        { money: 'kjjMoney', account: 'kjjAccount', label: '科技借账号类型' },\r\n        {\r\n          money: 'kjczMoney',\r\n          account: 'kjczAccount',\r\n          label: '科技出资账号类型',\r\n        },\r\n        {\r\n          money: 'sbczMoney',\r\n          account: 'sbczAccount',\r\n          label: '守邦出资账号类型',\r\n        },\r\n      ]\r\n\r\n      // 校验每个金额>0时账号类型必填\r\n      for (const item of moneyAccountMap) {\r\n        if (Number(this.trialForm[item.money]) > 0 && !this.trialForm[item.account]) {\r\n          this.$message.warning(`请先选择${item.label}`)\r\n          return\r\n        }\r\n      }\r\n      var data = {\r\n        id: this.trialForm.id,\r\n        fxjProportion: this.trialForm.fxjProportion,\r\n        fxjMoney: this.trialForm.fxjMoney,\r\n        fxjAccount: this.trialForm.fxjAccount,\r\n        qdProportion: this.trialForm.qdProportion,\r\n        qdMoney: this.trialForm.qdMoney,\r\n        qdAccount: this.trialForm.qdAccount,\r\n        gmjProportion: this.trialForm.gmjProportion,\r\n        gmjMoney: this.trialForm.gmjMoney,\r\n        gmjAccount: this.trialForm.gmjAccount,\r\n        kjjProportion: this.trialForm.kjjProportion,\r\n        kjjMoney: this.trialForm.kjjMoney,\r\n        kjjAccount: this.trialForm.kjjAccount,\r\n        kjczProportion: this.trialForm.kjczProportion,\r\n        kjczMoney: this.trialForm.kjczMoney,\r\n        kjczAccount: this.trialForm.kjczAccount,\r\n        sbczProportion: this.trialForm.sbczProportion,\r\n        sbczMoney: this.trialForm.sbczMoney,\r\n        sbczAccount: this.trialForm.sbczAccount,\r\n        otherDebt: this.trialFormotherDebt,\r\n        totalMoney: this.trialFormtotal,\r\n        image: this.trialForm.image.map(item => item.response).join(','),\r\n        examineStatus: 1,\r\n        repaymentType: 0,\r\n      }\r\n      console.log(data)\r\n      sub_Trial_order(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.commuteopen = false\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除VIEW编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_account_loan(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_account_loan/vw_account_loan/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_account_loan_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    selectRow() {\r\n      // Implement the logic for selecting a row\r\n      console.log('Row selected:', this.vw_account_loanList)\r\n    },\r\n\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传图片失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style>\r\n.dialogBox .el-form-item__label {\r\n  width: 100px !important;\r\n}\r\n\r\n.dialogBox .el-form-item__content {\r\n  margin-left: 100px !important;\r\n}\r\n\r\n.settle_money {\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 5px;\r\n  display: inline-block;\r\n  padding: 5px 10px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding-top: 10px !important;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* New styles for the filter bar */\r\n.filter-container {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  margin-bottom: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.filter-item-wrapper {\r\n  flex: 1;\r\n  min-width: 180px;\r\n  padding: 0 5px;\r\n}\r\n\r\n.filter-item-wrapper .el-form-item {\r\n  margin-bottom: 0;\r\n  width: 100%;\r\n}\r\n\r\n.filter-item-wrapper .el-select,\r\n.filter-item-wrapper .el-input,\r\n.filter-item-wrapper .el-date-editor {\r\n  width: 100% !important;\r\n}\r\n\r\n.filter-button-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  white-space: nowrap;\r\n  padding-left: 10px;\r\n  margin-left: auto;\r\n}\r\n\r\n.filter-button-wrapper .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.filter-row .el-form-item {\r\n  margin-bottom: 10px;\r\n  width: 100%;\r\n}\r\n\r\n.filter-row .el-select,\r\n.filter-row .el-input,\r\n.filter-row .el-date-editor {\r\n  width: 100% !important;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 10px 0;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.filter-buttons .el-button {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 响应式布局调整 */\r\n@media screen and (max-width: 1400px) {\r\n  .filter-row .el-col-lg-4 {\r\n    width: 33.33%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 992px) {\r\n  .filter-row .el-col-md-8 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .filter-row .el-col-sm-12 {\r\n    width: 100%;\r\n  }\r\n}\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 2px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"]}]}