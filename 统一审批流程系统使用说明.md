# 统一审批流程系统使用说明

## 概述

本系统已经统一了法诉费用模块和车辆订单审批模块的审批流程，确保两个模块使用完全一致的审批状态码、流程节点和业务逻辑。

## 审批状态定义

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 通过 | 初始状态或单步通过 |
| 1 | 全部同意 | 所有审批节点都同意的最终状态 |
| 2 | 已拒绝 | 任何一个审批节点拒绝的最终状态 |
| 3 | 法诉主管审批 | 等待法诉主管审批 |
| 4 | 总监审批 | 等待总监审批 |
| 5 | 财务主管/总监抄送 | 等待财务部门确认 |
| 6 | 总经理/董事长审批(抄送) | 等待最高级别审批 |

## 审批流程规则

### 审批顺序
审批必须按照以下顺序进行：
```
3 (法诉主管审批) → 4 (总监审批) → 5 (财务主管/总监抄送) → 6 (总经理/董事长审批) → 1 (全部同意)
```

### 审批逻辑
1. **通过审批**：当前审批人同意后，系统自动进入下一个审批状态
2. **拒绝审批**：任何一个审批节点拒绝时，立即设置状态为2（已拒绝）
3. **最终状态**：当所有审批节点都同意时，最终状态设为1（全部同意）

### 权限控制
- **法诉主管**：可审批状态为3的申请
- **总监**：可审批状态为4的申请
- **财务主管/财务总监**：可审批状态为5的申请
- **总经理/董事长**：可审批状态为6的申请

## 后端API接口

### 统一审批接口

#### 1. 单个审批
```http
PUT /litigation_cost_approval/litigation_cost_approval/approve
PUT /vw_car_order_examine/vw_car_order_examine/approve
```

请求体：
```json
{
  "id": 123,
  "approvalAction": "approve", // "approve" 或 "reject"
  "rejectReason": "拒绝原因（拒绝时必填）",
  "remark": "审批备注（可选）"
}
```

#### 2. 批量审批
```http
PUT /litigation_cost_approval/litigation_cost_approval/batchApprove
PUT /vw_car_order_examine/vw_car_order_examine/batchApprove
```

请求体：
```json
{
  "ids": [123, 124, 125],
  "approvalAction": "approve", // "approve" 或 "reject"
  "rejectReason": "拒绝原因（拒绝时必填）",
  "remark": "审批备注（可选）"
}
```

#### 3. 获取待审批列表
```http
GET /litigation_cost_approval/litigation_cost_approval/pending
GET /vw_car_order_examine/vw_car_order_examine/pending
```

#### 4. 获取审批状态统计
```http
GET /litigation_cost_approval/litigation_cost_approval/statistics
GET /vw_car_order_examine/vw_car_order_examine/statistics
```

## 前端组件使用

### 1. 导入统一审批组件
```javascript
import ApprovalDialog from '@/components/ApprovalDialog'
import BatchApprovalDialog from '@/components/BatchApprovalDialog'
import { approvalMixin } from '@/api/common/approval'
import { getApprovalStatusText, getApprovalStatusColor } from '@/utils/approvalConstants'
```

### 2. 使用审批混入
```javascript
export default {
  mixins: [approvalMixin],
  components: {
    ApprovalDialog,
    BatchApprovalDialog
  },
  // ...
}
```

### 3. 模板中使用组件
```vue
<template>
  <!-- 单个审批对话框 -->
  <ApprovalDialog
    v-model="approvalDialogVisible"
    title="审批"
    :approval-data="currentApprovalRecord"
    :user-role="userRole"
    @approve="handleApproval"
  />

  <!-- 批量审批对话框 -->
  <BatchApprovalDialog
    v-model="batchApprovalDialogVisible"
    :selected-count="selectedApprovalIds.length"
    :selected-ids="selectedApprovalIds"
    @batch-approve="handleBatchApproval"
  />
</template>
```

### 4. 状态显示
```vue
<template>
  <el-tag :type="getApprovalStatusColor(record.approvalStatus)">
    {{ getApprovalStatusText(record.approvalStatus) }}
  </el-tag>
</template>
```

## 数据库字段说明

### 统一的审批字段
- `approval_status` (INTEGER): 审批状态
- `reject_reason` (VARCHAR): 拒绝原因
- `approve_by` (VARCHAR): 审批人姓名
- `approve_role` (VARCHAR): 审批人角色
- `application_time` (DATETIME): 申请时间
- `application_by` (VARCHAR): 申请人
- `approve_time` (DATETIME): 审批时间
- `approval_history` (TEXT): 审批历史记录

## 注意事项

1. **类型转换**：数据库中的 `approval_status` 字段如果是VARCHAR类型，系统会自动进行类型转换
2. **权限检查**：系统会自动检查当前用户是否有权限进行当前阶段的审批
3. **历史记录**：每次审批操作都会记录在 `approval_history` 字段中
4. **最终状态**：状态为1（全部同意）或2（已拒绝）的记录不能再次审批

## 扩展新模块

如果需要为新模块添加统一审批功能：

1. **实体类继承**：让实体类继承 `BaseApprovalEntity`
2. **服务接口实现**：让服务接口继承 `IBaseApprovalService<T>`
3. **控制器继承**：让控制器继承 `BaseApprovalController<T, S>`
4. **前端使用**：使用统一的审批组件和混入

这样可以确保所有模块的审批流程完全一致。
