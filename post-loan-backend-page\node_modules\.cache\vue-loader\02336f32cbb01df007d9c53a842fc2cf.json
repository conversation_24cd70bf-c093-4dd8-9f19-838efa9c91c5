{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalDialog\\index.vue?vue&type=style&index=0&id=623d0f44&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalDialog\\index.vue", "mtime": 1754013068107}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHByb3ZhbC1kaWFsb2cgewogIG1heC1oZWlnaHQ6IDYwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5hcHByb3ZhbC1pbmZvIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9CgouYXBwcm92YWwtcHJvZ3Jlc3MgewogIG1hcmdpbjogMjBweCAwOwp9CgouYXBwcm92YWwtaGlzdG9yeSB7CiAgbWFyZ2luLXRvcDogMjBweDsKfQoKLmhpc3RvcnktY29udGVudCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICBwYWRkaW5nOiAxMHB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBtYXgtaGVpZ2h0OiAxNTBweDsKICBvdmVyZmxvdy15OiBhdXRvOwp9CgouaGlzdG9yeS1jb250ZW50IHByZSB7CiAgbWFyZ2luOiAwOwogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsKICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7CiAgZm9udC1zaXplOiAxMnB4OwogIGxpbmUtaGVpZ2h0OiAxLjQ7Cn0KCi5kaWFsb2ctZm9vdGVyIHsKICB0ZXh0LWFsaWduOiByaWdodDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2OA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ApprovalDialog", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"approval-dialog\">\n      <!-- 审批信息展示 -->\n      <div class=\"approval-info\" v-if=\"approvalData\">\n        <el-descriptions title=\"审批信息\" :column=\"2\" border>\n          <el-descriptions-item label=\"当前状态\">\n            <el-tag :type=\"getApprovalStatusColor(approvalData.approvalStatus)\">\n              {{ getApprovalStatusText(approvalData.approvalStatus) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请人\">\n            {{ approvalData.applicationBy || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请时间\">\n            {{ approvalData.applicationTime || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"审批人\">\n            {{ approvalData.approveBy || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <!-- 审批流程进度 -->\n      <div class=\"approval-progress\" v-if=\"showProgress\">\n        <el-steps :active=\"getCurrentStepIndex(approvalData.approvalStatus)\" finish-status=\"success\">\n          <el-step\n            v-for=\"step in APPROVAL_STEPS\"\n            :key=\"step.status\"\n            :title=\"step.title\"\n            :description=\"step.description\"\n          ></el-step>\n        </el-steps>\n      </div>\n\n      <!-- 审批操作表单 -->\n      <el-form ref=\"approvalForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 审批历史 -->\n      <div class=\"approval-history\" v-if=\"approvalData && approvalData.approvalHistory\">\n        <el-divider content-position=\"left\">审批历史</el-divider>\n        <div class=\"history-content\">\n          <pre>{{ approvalData.approvalHistory }}</pre>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_STATUS,\n  APPROVAL_ACTION,\n  APPROVAL_STEPS,\n  getApprovalStatusText,\n  getApprovalStatusColor,\n  getCurrentStepIndex,\n  canApprove\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'ApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 对话框标题\n    title: {\n      type: String,\n      default: '审批'\n    },\n    // 审批数据\n    approvalData: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示进度条\n    showProgress: {\n      type: Boolean,\n      default: true\n    },\n    // 用户角色\n    userRole: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      APPROVAL_STATUS,\n      APPROVAL_ACTION,\n      APPROVAL_STEPS,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    getApprovalStatusText,\n    getApprovalStatusColor,\n    getCurrentStepIndex,\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.approvalForm) {\n          this.$refs.approvalForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 提交审批\n    handleSubmit() {\n      this.$refs.approvalForm.validate((valid) => {\n        if (valid) {\n          // 检查用户权限\n          if (!canApprove(this.approvalData.approvalStatus, this.userRole)) {\n            this.$message.error('您没有权限进行当前阶段的审批')\n            return\n          }\n\n          this.loading = true\n          const approvalRequest = {\n            id: this.approvalData.id,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('approve', approvalRequest)\n        }\n      })\n    },\n\n    // 审批完成回调\n    onApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 审批失败回调\n    onApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-dialog {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.approval-info {\n  margin-bottom: 20px;\n}\n\n.approval-progress {\n  margin: 20px 0;\n}\n\n.approval-history {\n  margin-top: 20px;\n}\n\n.history-content {\n  background-color: #f5f5f5;\n  padding: 10px;\n  border-radius: 4px;\n  max-height: 150px;\n  overflow-y: auto;\n}\n\n.history-content pre {\n  margin: 0;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}