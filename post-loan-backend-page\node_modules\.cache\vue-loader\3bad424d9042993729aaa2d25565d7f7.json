{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalDialog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalDialog\\index.vue", "mtime": 1754013068107}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ApprovalDialog", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"approval-dialog\">\n      <!-- 审批信息展示 -->\n      <div class=\"approval-info\" v-if=\"approvalData\">\n        <el-descriptions title=\"审批信息\" :column=\"2\" border>\n          <el-descriptions-item label=\"当前状态\">\n            <el-tag :type=\"getApprovalStatusColor(approvalData.approvalStatus)\">\n              {{ getApprovalStatusText(approvalData.approvalStatus) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请人\">\n            {{ approvalData.applicationBy || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"申请时间\">\n            {{ approvalData.applicationTime || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"审批人\">\n            {{ approvalData.approveBy || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <!-- 审批流程进度 -->\n      <div class=\"approval-progress\" v-if=\"showProgress\">\n        <el-steps :active=\"getCurrentStepIndex(approvalData.approvalStatus)\" finish-status=\"success\">\n          <el-step\n            v-for=\"step in APPROVAL_STEPS\"\n            :key=\"step.status\"\n            :title=\"step.title\"\n            :description=\"step.description\"\n          ></el-step>\n        </el-steps>\n      </div>\n\n      <!-- 审批操作表单 -->\n      <el-form ref=\"approvalForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 审批历史 -->\n      <div class=\"approval-history\" v-if=\"approvalData && approvalData.approvalHistory\">\n        <el-divider content-position=\"left\">审批历史</el-divider>\n        <div class=\"history-content\">\n          <pre>{{ approvalData.approvalHistory }}</pre>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_STATUS,\n  APPROVAL_ACTION,\n  APPROVAL_STEPS,\n  getApprovalStatusText,\n  getApprovalStatusColor,\n  getCurrentStepIndex,\n  canApprove\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'ApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 对话框标题\n    title: {\n      type: String,\n      default: '审批'\n    },\n    // 审批数据\n    approvalData: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示进度条\n    showProgress: {\n      type: Boolean,\n      default: true\n    },\n    // 用户角色\n    userRole: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      APPROVAL_STATUS,\n      APPROVAL_ACTION,\n      APPROVAL_STEPS,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    getApprovalStatusText,\n    getApprovalStatusColor,\n    getCurrentStepIndex,\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.approvalForm) {\n          this.$refs.approvalForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 提交审批\n    handleSubmit() {\n      this.$refs.approvalForm.validate((valid) => {\n        if (valid) {\n          // 检查用户权限\n          if (!canApprove(this.approvalData.approvalStatus, this.userRole)) {\n            this.$message.error('您没有权限进行当前阶段的审批')\n            return\n          }\n\n          this.loading = true\n          const approvalRequest = {\n            id: this.approvalData.id,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('approve', approvalRequest)\n        }\n      })\n    },\n\n    // 审批完成回调\n    onApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 审批失败回调\n    onApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-dialog {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.approval-info {\n  margin-bottom: 20px;\n}\n\n.approval-progress {\n  margin: 20px 0;\n}\n\n.approval-history {\n  margin-top: 20px;\n}\n\n.history-content {\n  background-color: #f5f5f5;\n  padding: 10px;\n  border-radius: 4px;\n  max-height: 150px;\n  overflow-y: auto;\n}\n\n.history-content pre {\n  margin: 0;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}