{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.number.constructor.js\");\nvar _approvalConstants = require(\"@/utils/approvalConstants\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'BatchApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 选中的记录数量\n    selectedCount: {\n      type: Number,\n      default: 0\n    },\n    // 选中的记录ID列表\n    selectedIds: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      APPROVAL_ACTION: _approvalConstants.APPROVAL_ACTION,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [{\n          required: true,\n          message: '请选择审批结果',\n          trigger: 'change'\n        }],\n        rejectReason: [{\n          required: true,\n          message: '请输入拒绝原因',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: {\n    visible: {\n      get: function get() {\n        return this.value;\n      },\n      set: function set(val) {\n        this.$emit('input', val);\n      }\n    }\n  },\n  watch: {\n    value: function value(newVal) {\n      if (newVal) {\n        this.resetForm();\n      }\n    },\n    'form.approvalAction': function formApprovalAction(newVal) {\n      if (newVal !== _approvalConstants.APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = '';\n      }\n    }\n  },\n  methods: {\n    // 重置表单\n    resetForm: function resetForm() {\n      var _this = this;\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      };\n      this.$nextTick(function () {\n        if (_this.$refs.batchForm) {\n          _this.$refs.batchForm.clearValidate();\n        }\n      });\n    },\n    // 关闭对话框\n    handleClose: function handleClose() {\n      this.visible = false;\n      this.resetForm();\n    },\n    // 获取警告提示文本\n    getWarningText: function getWarningText() {\n      if (this.form.approvalAction === _approvalConstants.APPROVAL_ACTION.APPROVE) {\n        return \"\\u786E\\u8BA4\\u8981\\u6279\\u91CF\\u901A\\u8FC7\\u8FD9 \".concat(this.selectedCount, \" \\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u64A4\\u9500\\u3002\");\n      } else if (this.form.approvalAction === _approvalConstants.APPROVAL_ACTION.REJECT) {\n        return \"\\u786E\\u8BA4\\u8981\\u6279\\u91CF\\u62D2\\u7EDD\\u8FD9 \".concat(this.selectedCount, \" \\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u64A4\\u9500\\u3002\");\n      }\n      return '';\n    },\n    // 提交批量审批\n    handleSubmit: function handleSubmit() {\n      var _this2 = this;\n      if (this.selectedIds.length === 0) {\n        this.$message.error('请先选择要审批的记录');\n        return;\n      }\n      this.$refs.batchForm.validate(function (valid) {\n        if (valid) {\n          _this2.loading = true;\n          var batchRequest = {\n            ids: _this2.selectedIds,\n            approvalAction: _this2.form.approvalAction,\n            rejectReason: _this2.form.rejectReason,\n            remark: _this2.form.remark\n          };\n          _this2.$emit('batch-approve', batchRequest);\n        }\n      });\n    },\n    // 批量审批完成回调\n    onBatchApprovalComplete: function onBatchApprovalComplete() {\n      this.loading = false;\n      this.handleClose();\n    },\n    // 批量审批失败回调\n    onBatchApprovalError: function onBatchApprovalError() {\n      this.loading = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["_approvalConstants", "require", "name", "props", "value", "type", "Boolean", "default", "selectedCount", "Number", "selectedIds", "Array", "data", "APPROVAL_ACTION", "loading", "form", "approvalAction", "rejectReason", "remark", "rules", "required", "message", "trigger", "computed", "visible", "get", "set", "val", "$emit", "watch", "newVal", "resetForm", "formApprovalAction", "REJECT", "methods", "_this", "$nextTick", "$refs", "batchForm", "clearValidate", "handleClose", "getWarningText", "APPROVE", "concat", "handleSubmit", "_this2", "length", "$message", "error", "validate", "valid", "batchRequest", "ids", "onBatchApprovalComplete", "onBatchApprovalError"], "sources": ["src/components/BatchApprovalDialog/index.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量审批\"\n    :visible.sync=\"visible\"\n    width=\"500px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"batch-approval-dialog\">\n      <!-- 选中记录信息 -->\n      <div class=\"selected-info\">\n        <el-alert\n          :title=\"`已选择 ${selectedCount} 条记录进行批量审批`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n\n      <!-- 批量审批表单 -->\n      <el-form ref=\"batchForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">批量通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">批量拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 警告提示 -->\n      <div class=\"warning-tip\" v-if=\"form.approvalAction\">\n        <el-alert\n          :title=\"getWarningText()\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_ACTION\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'BatchApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 选中的记录数量\n    selectedCount: {\n      type: Number,\n      default: 0\n    },\n    // 选中的记录ID列表\n    selectedIds: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      APPROVAL_ACTION,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.batchForm) {\n          this.$refs.batchForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 获取警告提示文本\n    getWarningText() {\n      if (this.form.approvalAction === APPROVAL_ACTION.APPROVE) {\n        return `确认要批量通过这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      } else if (this.form.approvalAction === APPROVAL_ACTION.REJECT) {\n        return `确认要批量拒绝这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      }\n      return ''\n    },\n\n    // 提交批量审批\n    handleSubmit() {\n      if (this.selectedIds.length === 0) {\n        this.$message.error('请先选择要审批的记录')\n        return\n      }\n\n      this.$refs.batchForm.validate((valid) => {\n        if (valid) {\n          this.loading = true\n          const batchRequest = {\n            ids: this.selectedIds,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('batch-approve', batchRequest)\n        }\n      })\n    },\n\n    // 批量审批完成回调\n    onBatchApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 批量审批失败回调\n    onBatchApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.batch-approval-dialog {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.selected-info {\n  margin-bottom: 20px;\n}\n\n.warning-tip {\n  margin-top: 20px;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": ";;;;;;;AAsEA,IAAAA,kBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAIA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,aAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,WAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA,EAAAA,kCAAA;MACAC,OAAA;MACAC,IAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAH,cAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,YAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAArB,KAAA;MACA;MACAsB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,UAAAD,GAAA;MACA;IACA;EACA;EACAE,KAAA;IACAzB,KAAA,WAAAA,MAAA0B,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,SAAA;MACA;IACA;IACA,gCAAAC,mBAAAF,MAAA;MACA,IAAAA,MAAA,KAAAjB,kCAAA,CAAAoB,MAAA;QACA,KAAAlB,IAAA,CAAAE,YAAA;MACA;IACA;EACA;EACAiB,OAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAI,KAAA;MACA,KAAApB,IAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA,KAAAkB,SAAA;QACA,IAAAD,KAAA,CAAAE,KAAA,CAAAC,SAAA;UACAH,KAAA,CAAAE,KAAA,CAAAC,SAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAhB,OAAA;MACA,KAAAO,SAAA;IACA;IAEA;IACAU,cAAA,WAAAA,eAAA;MACA,SAAA1B,IAAA,CAAAC,cAAA,KAAAH,kCAAA,CAAA6B,OAAA;QACA,2DAAAC,MAAA,MAAAnC,aAAA;MACA,gBAAAO,IAAA,CAAAC,cAAA,KAAAH,kCAAA,CAAAoB,MAAA;QACA,2DAAAU,MAAA,MAAAnC,aAAA;MACA;MACA;IACA;IAEA;IACAoC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAnC,WAAA,CAAAoC,MAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAX,KAAA,CAAAC,SAAA,CAAAW,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAL,MAAA,CAAA/B,OAAA;UACA,IAAAqC,YAAA;YACAC,GAAA,EAAAP,MAAA,CAAAnC,WAAA;YACAM,cAAA,EAAA6B,MAAA,CAAA9B,IAAA,CAAAC,cAAA;YACAC,YAAA,EAAA4B,MAAA,CAAA9B,IAAA,CAAAE,YAAA;YACAC,MAAA,EAAA2B,MAAA,CAAA9B,IAAA,CAAAG;UACA;UAEA2B,MAAA,CAAAjB,KAAA,kBAAAuB,YAAA;QACA;MACA;IACA;IAEA;IACAE,uBAAA,WAAAA,wBAAA;MACA,KAAAvC,OAAA;MACA,KAAA0B,WAAA;IACA;IAEA;IACAc,oBAAA,WAAAA,qBAAA;MACA,KAAAxC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}