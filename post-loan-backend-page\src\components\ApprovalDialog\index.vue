<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="approval-dialog">
      <!-- 审批信息展示 -->
      <div class="approval-info" v-if="approvalData">
        <el-descriptions title="审批信息" :column="2" border>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getApprovalStatusColor(approvalData.approvalStatus)">
              {{ getApprovalStatusText(approvalData.approvalStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ approvalData.applicationBy || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ approvalData.applicationTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="审批人">
            {{ approvalData.approveBy || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 审批流程进度 -->
      <div class="approval-progress" v-if="showProgress">
        <el-steps :active="getCurrentStepIndex(approvalData.approvalStatus)" finish-status="success">
          <el-step
            v-for="step in APPROVAL_STEPS"
            :key="step.status"
            :title="step.title"
            :description="step.description"
          ></el-step>
        </el-steps>
      </div>

      <!-- 审批操作表单 -->
      <el-form ref="approvalForm" :model="form" :rules="rules" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="审批结果" prop="approvalAction">
          <el-radio-group v-model="form.approvalAction">
            <el-radio :label="APPROVAL_ACTION.APPROVE">通过</el-radio>
            <el-radio :label="APPROVAL_ACTION.REJECT">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          label="拒绝原因" 
          prop="rejectReason" 
          v-if="form.approvalAction === APPROVAL_ACTION.REJECT"
        >
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>

        <el-form-item label="审批备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入审批备注（可选）"
          ></el-input>
        </el-form-item>
      </el-form>

      <!-- 审批历史 -->
      <div class="approval-history" v-if="approvalData && approvalData.approvalHistory">
        <el-divider content-position="left">审批历史</el-divider>
        <div class="history-content">
          <pre>{{ approvalData.approvalHistory }}</pre>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  APPROVAL_STATUS,
  APPROVAL_ACTION,
  APPROVAL_STEPS,
  getApprovalStatusText,
  getApprovalStatusColor,
  getCurrentStepIndex,
  canApprove
} from '@/utils/approvalConstants'

export default {
  name: 'ApprovalDialog',
  props: {
    // 是否显示对话框
    value: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    title: {
      type: String,
      default: '审批'
    },
    // 审批数据
    approvalData: {
      type: Object,
      default: () => ({})
    },
    // 是否显示进度条
    showProgress: {
      type: Boolean,
      default: true
    },
    // 用户角色
    userRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      APPROVAL_STATUS,
      APPROVAL_ACTION,
      APPROVAL_STEPS,
      loading: false,
      form: {
        approvalAction: '',
        rejectReason: '',
        remark: ''
      },
      rules: {
        approvalAction: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        rejectReason: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    value(newVal) {
      if (newVal) {
        this.resetForm()
      }
    },
    'form.approvalAction'(newVal) {
      if (newVal !== APPROVAL_ACTION.REJECT) {
        this.form.rejectReason = ''
      }
    }
  },
  methods: {
    getApprovalStatusText,
    getApprovalStatusColor,
    getCurrentStepIndex,

    // 重置表单
    resetForm() {
      this.form = {
        approvalAction: '',
        rejectReason: '',
        remark: ''
      }
      this.$nextTick(() => {
        if (this.$refs.approvalForm) {
          this.$refs.approvalForm.clearValidate()
        }
      })
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.resetForm()
    },

    // 提交审批
    handleSubmit() {
      this.$refs.approvalForm.validate((valid) => {
        if (valid) {
          // 检查用户权限
          if (!canApprove(this.approvalData.approvalStatus, this.userRole)) {
            this.$message.error('您没有权限进行当前阶段的审批')
            return
          }

          this.loading = true
          const approvalRequest = {
            id: this.approvalData.id,
            approvalAction: this.form.approvalAction,
            rejectReason: this.form.rejectReason,
            remark: this.form.remark
          }

          this.$emit('approve', approvalRequest)
        }
      })
    },

    // 审批完成回调
    onApprovalComplete() {
      this.loading = false
      this.handleClose()
    },

    // 审批失败回调
    onApprovalError() {
      this.loading = false
    }
  }
}
</script>

<style scoped>
.approval-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.approval-info {
  margin-bottom: 20px;
}

.approval-progress {
  margin: 20px 0;
}

.approval-history {
  margin-top: 20px;
}

.history-content {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 150px;
  overflow-y: auto;
}

.history-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}
</style>
