package com.ruoyi.common.service;

import com.ruoyi.common.core.domain.BaseApprovalEntity;
import com.ruoyi.common.utils.SecurityUtils;
import java.util.Date;

/**
 * 统一审批流程服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface IBaseApprovalService<T extends BaseApprovalEntity> {

    /**
     * 统一审批处理方法
     * 
     * @param entity 审批实体
     * @param approvalAction 审批动作（approve-通过，reject-拒绝）
     * @param rejectReason 拒绝原因
     * @return 结果
     */
    default int processApproval(T entity, String approvalAction, String rejectReason) {
        // 获取当前用户信息
        String currentUser = SecurityUtils.getUsername();
        String userRole = SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName();
        
        // 检查是否有权限审批
        if (!entity.canApprove(userRole)) {
            throw new RuntimeException("您没有权限进行当前阶段的审批，当前状态为【" + entity.getStatusDescription() + "】");
        }
        
        // 检查是否为最终状态
        if (entity.isFinalStatus()) {
            throw new RuntimeException("该申请已处于最终状态，无法再次审批");
        }
        
        // 处理审批结果
        if ("reject".equals(approvalAction)) {
            // 拒绝：立即设置为拒绝状态
            entity.setApprovalStatus(BaseApprovalEntity.STATUS_REJECTED);
            entity.setRejectReason(rejectReason);
        } else if ("approve".equals(approvalAction)) {
            // 通过：进入下一个审批状态
            Integer nextStatus = entity.getNextApprovalStatus();
            entity.setApprovalStatus(nextStatus);
            entity.setRejectReason(null);
        } else {
            throw new RuntimeException("无效的审批动作");
        }
        
        // 设置审批信息
        entity.setApproveBy(currentUser);
        entity.setApproveRole(userRole);
        entity.setApproveTime(new Date());
        entity.setCurrentApprover(currentUser);
        
        // 记录审批历史
        String approvalRecord = String.format("[%s] %s %s - %s",
            new Date(), userRole, currentUser,
            "reject".equals(approvalAction) ? "拒绝：" + rejectReason : "通过");
            
        String currentHistory = entity.getApprovalHistory();
        if (currentHistory == null || currentHistory.trim().isEmpty()) {
            entity.setApprovalHistory(approvalRecord);
        } else {
            entity.setApprovalHistory(currentHistory + "\n" + approvalRecord);
        }
        
        // 调用具体实现的更新方法
        return updateApprovalEntity(entity);
    }
    
    /**
     * 批量审批处理方法
     * 
     * @param ids 实体ID列表
     * @param approvalAction 审批动作
     * @param rejectReason 拒绝原因
     * @return 结果
     */
    default int processBatchApproval(java.util.List<Long> ids, String approvalAction, String rejectReason) {
        int successCount = 0;
        for (Long id : ids) {
            try {
                T entity = selectById(id);
                if (entity != null) {
                    processApproval(entity, approvalAction, rejectReason);
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他记录
                System.err.println("批量审批失败，ID: " + id + ", 错误: " + e.getMessage());
            }
        }
        return successCount;
    }
    
    /**
     * 根据用户角色获取待审批列表
     * 
     * @param userRole 用户角色
     * @return 待审批列表
     */
    java.util.List<T> selectPendingApprovalList(String userRole);
    
    /**
     * 根据ID查询实体
     * 
     * @param id 实体ID
     * @return 实体对象
     */
    T selectById(Long id);
    
    /**
     * 更新审批实体
     * 
     * @param entity 审批实体
     * @return 结果
     */
    int updateApprovalEntity(T entity);
    
    /**
     * 获取审批状态统计
     * 
     * @return 统计结果
     */
    java.util.List<java.util.Map<String, Object>> getApprovalStatistics();
}
