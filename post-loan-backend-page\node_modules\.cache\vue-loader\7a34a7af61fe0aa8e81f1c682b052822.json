{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\BatchApprovalDialog\\index.vue?vue&type=style&index=0&id=46119188&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\BatchApprovalDialog\\index.vue", "mtime": 1754013097602}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5iYXRjaC1hcHByb3ZhbC1kaWFsb2cgewogIG1heC1oZWlnaHQ6IDUwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5zZWxlY3RlZC1pbmZvIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Cgoud2FybmluZy10aXAgewogIG1hcmdpbi10b3A6IDIwcHg7Cn0KCi5kaWFsb2ctZm9vdGVyIHsKICB0ZXh0LWFsaWduOiByaWdodDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0MA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/BatchApprovalDialog", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量审批\"\n    :visible.sync=\"visible\"\n    width=\"500px\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"batch-approval-dialog\">\n      <!-- 选中记录信息 -->\n      <div class=\"selected-info\">\n        <el-alert\n          :title=\"`已选择 ${selectedCount} 条记录进行批量审批`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n\n      <!-- 批量审批表单 -->\n      <el-form ref=\"batchForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"margin-top: 20px;\">\n        <el-form-item label=\"审批结果\" prop=\"approvalAction\">\n          <el-radio-group v-model=\"form.approvalAction\">\n            <el-radio :label=\"APPROVAL_ACTION.APPROVE\">批量通过</el-radio>\n            <el-radio :label=\"APPROVAL_ACTION.REJECT\">批量拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item \n          label=\"拒绝原因\" \n          prop=\"rejectReason\" \n          v-if=\"form.approvalAction === APPROVAL_ACTION.REJECT\"\n        >\n          <el-input\n            v-model=\"form.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"审批备注\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入审批备注（可选）\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 警告提示 -->\n      <div class=\"warning-tip\" v-if=\"form.approvalAction\">\n        <el-alert\n          :title=\"getWarningText()\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        ></el-alert>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {\n  APPROVAL_ACTION\n} from '@/utils/approvalConstants'\n\nexport default {\n  name: 'BatchApprovalDialog',\n  props: {\n    // 是否显示对话框\n    value: {\n      type: Boolean,\n      default: false\n    },\n    // 选中的记录数量\n    selectedCount: {\n      type: Number,\n      default: 0\n    },\n    // 选中的记录ID列表\n    selectedIds: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      APPROVAL_ACTION,\n      loading: false,\n      form: {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      },\n      rules: {\n        approvalAction: [\n          { required: true, message: '请选择审批结果', trigger: 'change' }\n        ],\n        rejectReason: [\n          { required: true, message: '请输入拒绝原因', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    }\n  },\n  watch: {\n    value(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n    'form.approvalAction'(newVal) {\n      if (newVal !== APPROVAL_ACTION.REJECT) {\n        this.form.rejectReason = ''\n      }\n    }\n  },\n  methods: {\n    // 重置表单\n    resetForm() {\n      this.form = {\n        approvalAction: '',\n        rejectReason: '',\n        remark: ''\n      }\n      this.$nextTick(() => {\n        if (this.$refs.batchForm) {\n          this.$refs.batchForm.clearValidate()\n        }\n      })\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.visible = false\n      this.resetForm()\n    },\n\n    // 获取警告提示文本\n    getWarningText() {\n      if (this.form.approvalAction === APPROVAL_ACTION.APPROVE) {\n        return `确认要批量通过这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      } else if (this.form.approvalAction === APPROVAL_ACTION.REJECT) {\n        return `确认要批量拒绝这 ${this.selectedCount} 条记录吗？此操作不可撤销。`\n      }\n      return ''\n    },\n\n    // 提交批量审批\n    handleSubmit() {\n      if (this.selectedIds.length === 0) {\n        this.$message.error('请先选择要审批的记录')\n        return\n      }\n\n      this.$refs.batchForm.validate((valid) => {\n        if (valid) {\n          this.loading = true\n          const batchRequest = {\n            ids: this.selectedIds,\n            approvalAction: this.form.approvalAction,\n            rejectReason: this.form.rejectReason,\n            remark: this.form.remark\n          }\n\n          this.$emit('batch-approve', batchRequest)\n        }\n      })\n    },\n\n    // 批量审批完成回调\n    onBatchApprovalComplete() {\n      this.loading = false\n      this.handleClose()\n    },\n\n    // 批量审批失败回调\n    onBatchApprovalError() {\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.batch-approval-dialog {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.selected-info {\n  margin-bottom: 20px;\n}\n\n.warning-tip {\n  margin-top: 20px;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}