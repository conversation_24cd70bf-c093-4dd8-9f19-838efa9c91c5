package com.ruoyi.common.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.BaseApprovalEntity;
import com.ruoyi.common.service.IBaseApprovalService;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;

/**
 * 统一审批流程Controller基础类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public abstract class BaseApprovalController<T extends BaseApprovalEntity, S extends IBaseApprovalService<T>> extends BaseController {

    /**
     * 获取服务实例
     */
    protected abstract S getService();

    /**
     * 单个审批
     */
    public AjaxResult approve(@RequestBody ApprovalRequest request) {
        try {
            if (request.getId() == null) {
                return error("审批ID不能为空");
            }

            if (request.getApprovalAction() == null || request.getApprovalAction().trim().isEmpty()) {
                return error("请选择审批结果");
            }

            if ("reject".equals(request.getApprovalAction()) && 
                (request.getRejectReason() == null || request.getRejectReason().trim().isEmpty())) {
                return error("拒绝时必须提供拒绝原因");
            }

            T entity = getService().selectById(request.getId());
            if (entity == null) {
                return error("找不到对应的审批信息");
            }

            int result = getService().processApproval(entity, request.getApprovalAction(), request.getRejectReason());
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 批量审批
     */
    public AjaxResult batchApprove(@RequestBody BatchApprovalRequest request) {
        try {
            if (request.getIds() == null || request.getIds().isEmpty()) {
                return error("请选择要审批的记录");
            }

            if (request.getApprovalAction() == null || request.getApprovalAction().trim().isEmpty()) {
                return error("请选择审批结果");
            }

            if ("reject".equals(request.getApprovalAction()) && 
                (request.getRejectReason() == null || request.getRejectReason().trim().isEmpty())) {
                return error("拒绝时必须提供拒绝原因");
            }

            int result = getService().processBatchApproval(request.getIds(), request.getApprovalAction(), request.getRejectReason());
            return success("成功审批 " + result + " 条记录");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 获取待审批列表
     */
    public AjaxResult getPendingApprovalList() {
        try {
            String userRole = SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName();
            List<T> list = getService().selectPendingApprovalList(userRole);
            return success(list);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 获取审批状态统计
     */
    public AjaxResult getApprovalStatistics() {
        try {
            List<Map<String, Object>> statistics = getService().getApprovalStatistics();
            return success(statistics);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 审批请求DTO
     */
    public static class ApprovalRequest {
        private Long id;
        private String approvalAction; // approve-通过, reject-拒绝
        private String rejectReason;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getApprovalAction() {
            return approvalAction;
        }

        public void setApprovalAction(String approvalAction) {
            this.approvalAction = approvalAction;
        }

        public String getRejectReason() {
            return rejectReason;
        }

        public void setRejectReason(String rejectReason) {
            this.rejectReason = rejectReason;
        }
    }

    /**
     * 批量审批请求DTO
     */
    public static class BatchApprovalRequest {
        private List<Long> ids;
        private String approvalAction; // approve-通过, reject-拒绝
        private String rejectReason;

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }

        public String getApprovalAction() {
            return approvalAction;
        }

        public void setApprovalAction(String approvalAction) {
            this.approvalAction = approvalAction;
        }

        public String getRejectReason() {
            return rejectReason;
        }

        public void setRejectReason(String rejectReason) {
            this.rejectReason = rejectReason;
        }
    }
}
