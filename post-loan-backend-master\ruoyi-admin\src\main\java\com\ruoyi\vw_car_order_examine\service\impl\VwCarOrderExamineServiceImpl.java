package com.ruoyi.vw_car_order_examine.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vw_car_order_examine.mapper.VwCarOrderExamineMapper;
import com.ruoyi.vw_car_order_examine.domain.VwCarOrderExamine;
import com.ruoyi.vw_car_order_examine.service.IVwCarOrderExamineService;
import com.ruoyi.common.core.domain.BaseApprovalEntity;

/**
 * 找车费用审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class VwCarOrderExamineServiceImpl implements IVwCarOrderExamineService 
{
    @Autowired
    private VwCarOrderExamineMapper vwCarOrderExamineMapper;

    /**
     * 查询找车费用审批
     * 
     * @param id 找车费用审批主键
     * @return 找车费用审批
     */
    @Override
    public VwCarOrderExamine selectVwCarOrderExamineById(String id)
    {
        return vwCarOrderExamineMapper.selectVwCarOrderExamineById(id);
    }

    /**
     * 查询找车费用审批列表
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 找车费用审批
     */
    @Override
    public List<VwCarOrderExamine> selectVwCarOrderExamineList(VwCarOrderExamine vwCarOrderExamine)
    {
        return vwCarOrderExamineMapper.selectVwCarOrderExamineList(vwCarOrderExamine);
    }

    /**
     * 新增找车费用审批
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 结果
     */
    @Override
    public int insertVwCarOrderExamine(VwCarOrderExamine vwCarOrderExamine)
    {
        return vwCarOrderExamineMapper.insertVwCarOrderExamine(vwCarOrderExamine);
    }

    /**
     * 修改找车费用审批
     * 
     * @param vwCarOrderExamine 找车费用审批
     * @return 结果
     */
    @Override
    public int updateVwCarOrderExamine(VwCarOrderExamine vwCarOrderExamine)
    {
        return vwCarOrderExamineMapper.updateVwCarOrderExamine(vwCarOrderExamine);
    }

    /**
     * 批量删除找车费用审批
     * 
     * @param ids 需要删除的找车费用审批主键
     * @return 结果
     */
    @Override
    public int deleteVwCarOrderExamineByIds(String[] ids)
    {
        return vwCarOrderExamineMapper.deleteVwCarOrderExamineByIds(ids);
    }

    /**
     * 删除找车费用审批信息
     *
     * @param id 找车费用审批主键
     * @return 结果
     */
    @Override
    public int deleteVwCarOrderExamineById(String id)
    {
        return vwCarOrderExamineMapper.deleteVwCarOrderExamineById(id);
    }

    /**
     * 根据ID查询实体 - 实现基础接口方法
     */
    @Override
    public VwCarOrderExamine selectById(Long id) {
        return vwCarOrderExamineMapper.selectVwCarOrderExamineById(id.toString());
    }

    /**
     * 更新审批实体 - 实现基础接口方法
     */
    @Override
    public int updateApprovalEntity(VwCarOrderExamine entity) {
        return vwCarOrderExamineMapper.updateVwCarOrderExamine(entity);
    }

    /**
     * 根据用户角色获取待审批列表 - 实现基础接口方法
     */
    @Override
    public List<VwCarOrderExamine> selectPendingApprovalList(String userRole) {
        VwCarOrderExamine query = new VwCarOrderExamine();

        // 根据用户角色设置查询条件
        if ("法诉主管".equals(userRole)) {
            query.setApprovalStatus(BaseApprovalEntity.STATUS_PENDING);
        } else if ("总监".equals(userRole)) {
            query.setApprovalStatus(BaseApprovalEntity.STATUS_LEGAL_SUPERVISOR);
        } else if ("财务主管".equals(userRole) || "财务总监".equals(userRole)) {
            query.setApprovalStatus(BaseApprovalEntity.STATUS_DIRECTOR);
        } else if ("总经理".equals(userRole) || "董事长".equals(userRole)) {
            query.setApprovalStatus(BaseApprovalEntity.STATUS_FINANCE_CC);
        } else {
            // 其他角色返回空列表
            return new ArrayList<>();
        }

        return vwCarOrderExamineMapper.selectVwCarOrderExamineList(query);
    }

    /**
     * 获取审批状态统计 - 实现基础接口方法
     */
    @Override
    public List<Map<String, Object>> getApprovalStatistics() {
        // 这里可以根据实际需求实现统计逻辑
        List<Map<String, Object>> statistics = new ArrayList<>();
        // 暂时返回空列表，后续可以根据需要实现具体的统计逻辑
        return statistics;
    }
}
